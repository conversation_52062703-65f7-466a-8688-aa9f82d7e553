<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<artifactId>optimus</artifactId>
	<description>API server for optimus</description>
	<groupId>com.mykaarma</groupId>
	<modelVersion>4.0.0</modelVersion>
	<name>optimus</name>
	<version>1.45.1-QRC26</version>
	<build>
		<plugins>
			<plugin>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<groupId>org.springframework.boot</groupId>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.8</version>
				<executions>
					<execution>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>report</id>
						<phase>test</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>io.fabric8</groupId>
				<version>0.40.3</version>
				<artifactId>docker-maven-plugin</artifactId>
				<dependencies>
					<dependency>
						<artifactId>javax.activation-api</artifactId>
						<groupId>javax.activation</groupId>
						<scope>compile</scope>
						<version>1.2.0</version>
					</dependency>
				</dependencies>
				<configuration>
					<registry>${docker.image.prefix}/optimus-server</registry>
					<outputDirectory>.</outputDirectory>
					<images>
						<image>
							<name>${docker.image.prefix}/optimus-server:${project.version}</name>
							<build>
								<dockerFile>${project.basedir}/src/main/docker/Dockerfile</dockerFile>
								<contextDir>${project.basedir}/target</contextDir>
								<buildx>
									<platforms>
										<platform>${docker.platforms}</platform>
									</platforms>
								</buildx>
							</build>
						</image>
					</images>
					<resources>
						<resource>
							<targetPath>/</targetPath>
							<directory>${project.build.directory}</directory>
							<include>${project.build.finalName}.jar</include>
						</resource>
					</resources>
				</configuration>
				<executions>
					<execution>
						<id>docker-build</id>
						<goals>
							<goal>build</goal>
						</goals>
					</execution>
					<execution>
						<id>docker-push</id>
						<goals>
							<goal>push</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>17</source>
					<target>17</target>
				</configuration>
			</plugin>
		</plugins>
	</build>
	<dependencies>
		<dependency>
			<artifactId>spring-boot-starter-actuator</artifactId>
			<groupId>org.springframework.boot</groupId>
		</dependency>
		<dependency>
			<artifactId>spring-boot-starter-web</artifactId>
			<groupId>org.springframework.boot</groupId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<version>8.3.0</version>
		</dependency>
		<dependency>
			<artifactId>lombok</artifactId>
			<groupId>org.projectlombok</groupId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<artifactId>jackson-databind</artifactId>
			<groupId>com.fasterxml.jackson.core</groupId>
			<version>2.13.3</version>
		</dependency>
		<dependency>
			<artifactId>kie-ci</artifactId>
			<groupId>org.kie</groupId>
			<version>7.73.0.Final</version>
		</dependency>
		<dependency>
			<artifactId>drools-decisiontables</artifactId>
			<groupId>org.drools</groupId>
			<version>7.73.0.Final</version>
		</dependency>
		<dependency>
			<groupId>com.pubnub</groupId>
			<artifactId>pubnub-gson</artifactId>
			<version>6.3.6</version>
		</dependency>
		<dependency>
			<artifactId>commons-io</artifactId>
			<groupId>org.apache.commons</groupId>
			<version>1.3.2</version>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.10.1</version>
		</dependency>
		<dependency>
			<artifactId>commons-io</artifactId>
			<groupId>commons-io</groupId>
			<version>2.11.0</version>
		</dependency>
		<dependency>
			<artifactId>github-api</artifactId>
			<groupId>org.kohsuke</groupId>
			<version>1.313</version>
		</dependency>
		<dependency>
			<groupId>com.mykaarma</groupId>
			<artifactId>pani-puri-client</artifactId>
			<version>1.0.0</version>
		</dependency>
		<dependency>
			<artifactId>spring-boot-starter-test</artifactId>
			<groupId>org.springframework.boot</groupId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.junit</groupId>
					<artifactId>junit</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.mockito</groupId>
					<artifactId>mockito-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.mockito</groupId>
					<artifactId>mockito-all</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-mongodb</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<version>3.11.2</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.github.tomakehurst</groupId>
			<artifactId>wiremock-jre8-standalone</artifactId>
			<version>2.35.0</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mongodb</groupId>
			<artifactId>mongo-java-driver</artifactId>
			<version>3.5.0</version>
		</dependency>
		<dependency>
			<groupId>com.offbytwo.jenkins</groupId>
			<artifactId>jenkins-client</artifactId>
			<version>0.3.8</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-webflux</artifactId>
		</dependency>
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20210307</version>
		</dependency>
		<dependency>
			<groupId>com.github.tomakehurst</groupId>
			<artifactId>wiremock-standalone</artifactId>
			<version>2.27.2</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
			<version>1.4.2.Final</version>
		</dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct-processor</artifactId>
			<version>1.4.2.Final</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
		<dependency>
			<groupId>com.atlassian.jira</groupId>
			<artifactId>jira-rest-java-client-app</artifactId>
			<version>5.2.0</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-ecr</artifactId>
			<version>1.12.376</version>
		</dependency>
		<dependency>
			<groupId>com.vdurmont</groupId>
			<artifactId>semver4j</artifactId>
			<version>3.1.0</version>
		</dependency>
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
			<version>2.0.0</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>1.10.0</version>
		</dependency>
		<dependency>
			<groupId>io.reactivex.rxjava2</groupId>
			<artifactId>rxjava</artifactId>
			<version>2.2.19</version>
		</dependency>
		<dependency>
			<groupId>com.nimbusds</groupId>
			<artifactId>nimbus-jose-jwt</artifactId>
			<version>9.35</version>
		</dependency>
		<dependency>
			<groupId>com.google.api-client</groupId>
			<artifactId>google-api-client</artifactId>
			<version>2.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.google.http-client</groupId>
			<artifactId>google-http-client-jackson</artifactId>
			<version>1.21.0</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>30.1.1-jre</version>
		</dependency>
		<dependency>
			<groupId>com.github.ulisesbocchio</groupId>
			<artifactId>jasypt-spring-boot-starter</artifactId>
			<version>3.0.5</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
			<version>3.1.1</version>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
		</dependency>
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-core</artifactId>
		</dependency>
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.auth</groupId>
			<artifactId>google-auth-library-oauth2-http</artifactId>
			<version>1.19.0</version>
		</dependency>
		<dependency>
			<groupId>com.rabbitmq</groupId>
			<artifactId>amqp-client</artifactId>
			<version>5.20.0</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.amqp</groupId>
			<artifactId>spring-rabbit</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-databind</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.0.1</version>
		<relativePath />
	</parent>
	<properties>
		<docker.image.prefix>578061096415.dkr.ecr.us-east-1.amazonaws.com</docker.image.prefix>
		<java.version>17</java.version>
	</properties>
	<repositories>
		<repository>
			<id>central</id>
			<name>Central Repository</name>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
			<url>https://repo.maven.apache.org/maven2</url>
		</repository>
		<repository>
			<id>ci02.kaar.biz</id>
			<name>ci01.kaar.biz-releases</name>
			<url>https://qa-artifactory.mykaarma.com/artifactory/libs-release</url>
		</repository>
		<repository>
			<id>ci04.kaar.biz</id>
			<name>ci04.kaar.biz-snapshots</name>
			<url>https://qa-artifactory.mykaarma.com/artifactory/libs-snapshot</url>
		</repository>
		<repository>
			<id>atlassian-public</id>
			<url>https://packages.atlassian.com/maven/repository/public</url>
		</repository>
	</repositories>
</project>
