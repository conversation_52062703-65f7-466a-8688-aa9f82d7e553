package com.mykaarma.optimus.scheduler.jobs;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mykaarma.optimus.constants.StringConstants;
import com.mykaarma.optimus.deployment.BuildAndDeployService;
import com.mykaarma.optimus.deployment.DeploymentNotificationService;
import com.mykaarma.optimus.deployment.OptimusDeploymentActionsService;
import com.mykaarma.optimus.jpa.model.*;
import com.mykaarma.optimus.jpa.repository.*;
import com.mykaarma.optimus.model.dto.OptimusDeploymentActionsDTO;
import com.mykaarma.optimus.model.enums.*;
import com.mykaarma.optimus.model.panipuri.dto.TeamResponseDTO;
import com.mykaarma.optimus.mongo.model.OptimusDeploymentSubscription;
import com.mykaarma.optimus.mongo.repository.OptimusDeploymentSubscriptionRepository;
import com.mykaarma.optimus.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.kohsuke.github.GHContent;
import org.kohsuke.github.GHRef;
import org.kohsuke.github.GHRepository;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class MasterMigrationJob implements ScheduledJobsInterface{
	private final JiraService jiraService;
	private final ServiceRepository serviceRepository;

	private final ScheduledJobUtils scheduledJobUtils;

	private final GithubService githubService;
	private final OptimusDeploymentRepository odRepository;
	private final BuildAndDeployService buildAndDeployService;
	private final AwsUtils awsUtils;
	private final String registryId;

	private final AwxService awxService;

	private final DeploymentNotificationService deploymentNotificationService;
	private final String awxJobUrlFormatConsole;
	private final ObjectMapper mapper = new ObjectMapper();

	private final OptimusDeploymentTypeRepository optimusDeploymentTypeRepository;
	private final OptimusDeploymentActionsService optimusDeploymentActionsService;
	private final OptimusDeploymentSubscriptionRepository optimusDeploymentSubscriptionRepository;
	private final PaniPuriService paniPuriService;
	private final NotificationMessageHelper notificationMessageHelper;
	private final OptimusDeploymentHelper odHelper;
	private final GoogleChatUtils gChatUtils;
	private final String environment;

	private final String QA_MIGRATION_BRANCH = "QA-Migration";
	private final String QA_MIGRATION_BRANCH_CANARY = "QA-Migration-canary";

	private final String KAARMA_CONFIG_TAG_SERVICE = "kaarma-config--tag";
	private final String ENV_FILE_PARSING_REGEX = "export\\s+(\\w+)\\s*=\\s*([^\\s]+)";
	private final String TOMCAT = "tomcat";
	private final String ENV_FILE_REPO = "mykaarma/internal-systems";
	private final String ENV_FILE_BRANCH = "master" ;
	private final String QA_SUFFIX = "-QA" ;

	private final String TEAM = "EngProd";
	private final String PROD_ENVIRONMENT = "PRODUCTION" ;
	private final String STAGING_ENVIRONMENT = "STAGING" ;
	private final ExecutionRepository executionRepository;
	private final ServiceDeploymentInfoRepository serviceDeploymentInfoRepository;
	private final CustomWait customWait;
    private final DeploymentTargetRepository deploymentTargetRepository;
	private final OptimusDeploymentServiceRepository optimusDeploymentServiceRepository;
	private final String k8_SERVICE_NAME_FROM_AWX = "\"(.+?)\"";

	private final String k8_SERVICE_NAME_FROM_DEPLOYMENT_TARGET = "\"k8sName\"\\s*:\\s*\"(.+)\"";

	private final String ENV_FILE_PATH = "/kubernetes/%";



	public MasterMigrationJob(JiraService jiraService, ServiceRepository serviceRepository, ScheduledJobUtils scheduledJobUtils, GithubService githubService, OptimusDeploymentRepository odRepository,
							  BuildAndDeployService buildAndDeployService, AwsUtils awsUtils, AwxService awxService, DeploymentNotificationService deploymentNotificationService, OptimusDeploymentTypeRepository optimusDeploymentTypeRepository,
							  OptimusDeploymentActionsService optimusDeploymentActionsService, OptimusDeploymentSubscriptionRepository optimusDeploymentSubscriptionRepository, PaniPuriService paniPuriService,
							  NotificationMessageHelper notificationMessageHelper, @Value("${aws.ecr.registry.id}") final String registryId, GoogleChatUtils gChatUtils, @Value("${spring.profiles.active}") final String environment,
							  @Value("${awx.jobUrlFormatForConsole}") String awxJobUrlFormatConsole, ExecutionRepository executionRepository, ServiceDeploymentInfoRepository serviceDeploymentInfoRepository, CustomWait customWait, DeploymentTargetRepository deploymentTargetRepository, OptimusDeploymentServiceRepository optimusDeploymentServiceRepository, OptimusDeploymentHelper odHelper) {
		this.jiraService = jiraService;
		this.serviceRepository = serviceRepository;
		this.scheduledJobUtils = scheduledJobUtils;
		this.githubService = githubService;
		this.odRepository = odRepository;
		this.buildAndDeployService = buildAndDeployService;
		this.awsUtils = awsUtils;
		this.awxService = awxService;
		this.registryId = registryId;
		this.deploymentNotificationService = deploymentNotificationService;
		this.optimusDeploymentTypeRepository = optimusDeploymentTypeRepository;
		this.optimusDeploymentActionsService = optimusDeploymentActionsService;
		this.optimusDeploymentSubscriptionRepository = optimusDeploymentSubscriptionRepository;
		this.paniPuriService = paniPuriService;
		this.notificationMessageHelper = notificationMessageHelper;
		this.gChatUtils = gChatUtils;
		this.environment = environment;
		this.awxJobUrlFormatConsole = awxJobUrlFormatConsole;
		this.executionRepository = executionRepository;
		this.serviceDeploymentInfoRepository = serviceDeploymentInfoRepository;
		this.customWait = customWait;
        this.deploymentTargetRepository = deploymentTargetRepository;
		this.optimusDeploymentServiceRepository = optimusDeploymentServiceRepository;
		this.odHelper = odHelper ;
	}


	@Override
	public void run(String uuid) {
		if (!environment.equalsIgnoreCase("staging")) {
			return;
		}
			log.info("Master Migration job started");
		    runMasterMigration(getServiceEnvListThatNeedsToBeMigrated()) ;
	}

	private Map<String, List<ServiceEntity>> computeRepoToServiceMap(List<ServiceEntity> uiClientServices){
		Map<String, List<ServiceEntity>> repoToServiceMap = new HashMap<>() ;
		for(ServiceEntity service: uiClientServices){
			String repo = service.getRepositoryName();
			List<ServiceEntity> servicesInRepo = new ArrayList<>();
			if(!repoToServiceMap.containsKey(repo)){
				servicesInRepo.add(service);
				repoToServiceMap.put(repo, servicesInRepo);
			}
			else{
				repoToServiceMap.get(repo).add(service);
			}
		}
		return repoToServiceMap ;

	}
	public void sendUiClientServicesAndMavenServicesForBuildAndDeploy(OptimusDeployment optimusDeployment, List<OptimusDeploymentServiceEntity> mavenOdServices, List<OptimusDeploymentServiceEntity> uiClientOdServices) {
		log.info("Sending optimusDeployment {} for build and deploy", optimusDeployment.getUuid());
		OptimusDeploymentActionsDTO optimusDeploymentActionsDTO = new OptimusDeploymentActionsDTO();
		List<OptimusDeploymentServiceEntity> uiClientToBuild = new ArrayList<>();
		List<OptimusDeploymentServiceEntity> uiClientToBuildAndDeploy = new ArrayList<>();
		List<OptimusDeploymentServiceEntity> uiClientToDeploy = new ArrayList<>();
		if(uiClientOdServices != null) {
			for (OptimusDeploymentServiceEntity uiClientOdService : uiClientOdServices) {
				Boolean isImageExists = false ;
				// This is the custom Handling for api-gateway services which needs to be deployed but not build.
				if(uiClientOdService.getService().getName().contains(StringConstants.API_GATEWAY_GLOBAL.getValue())){
					uiClientToBuild.add(uiClientOdService);
					continue ;
				}
				if(uiClientOdService.getService().getName().contains("api-gateway-")){
					uiClientToDeploy.add(uiClientOdService);
					continue ;
				}
				if (!StringUtils.isEmpty(uiClientOdService.getService().getDockerImage())) {
					if(awsUtils.isImageExistsInECR(registryId, uiClientOdService.getService().getDockerImage(), uiClientOdService.getIssueBranchVersion())) {isImageExists = true ;}

					if(hasValidDeploymentTargets(uiClientOdService.getService().getDeploymentTargets())){
						if(!isImageExists){
							log.info("Client getting build and deployed is {} with Service UUID {}", uiClientOdService.getService().getName(), uiClientOdService.getUuid());
							uiClientToBuildAndDeploy.add(uiClientOdService);
						} else{
							log.info("Client getting deployed only is {} with Service UUID {}", uiClientOdService.getService().getName(), uiClientOdService.getUuid());
							uiClientToDeploy.add(uiClientOdService);
						}
					} else{
						if(!isImageExists){
							log.info("Client getting build only is {} with Service UUID {}", uiClientOdService.getService().getName(), uiClientOdService.getUuid());
							uiClientToBuild.add(uiClientOdService);
						}
					}
				} else {
					log.info("Client getting build only is {} with Service UUID {}", uiClientOdService.getService().getName(), uiClientOdService.getUuid());
					uiClientToBuild.add(uiClientOdService);
				}
			}
		}

		// Compute Optimus Deployment ActionsDTO and send it for build and deploy
		optimusDeploymentActionsDTO.setServicesWithoutPr(new ArrayList<>());
		if(!CollectionUtils.isEmpty(uiClientToBuildAndDeploy)) {
			OptimusDeploymentActionsDTO odActionsDTOForUiClientsToBuildAndDeploy = optimusDeploymentActionsService
					.createOptimusDeploymentServiceActionDTOWithoutPRs(uiClientToBuildAndDeploy,
							new ArrayList<>(List.of(OptimusDeploymentAction.BUILD, OptimusDeploymentAction.DEPLOY)));
			optimusDeploymentActionsDTO.getServicesWithoutPr().addAll(odActionsDTOForUiClientsToBuildAndDeploy.getServicesWithoutPr());
		}
		if(!CollectionUtils.isEmpty(uiClientToBuild)) {
			OptimusDeploymentActionsDTO odActionsDTOForUiClientsToBuild = optimusDeploymentActionsService
					.createOptimusDeploymentServiceActionDTOWithoutPRs(uiClientToBuild,
							new ArrayList<>(List.of(OptimusDeploymentAction.BUILD)));
			optimusDeploymentActionsDTO.getServicesWithoutPr().addAll(odActionsDTOForUiClientsToBuild.getServicesWithoutPr());
		}
		if(!CollectionUtils.isEmpty(uiClientToDeploy)) {
			OptimusDeploymentActionsDTO odActionsDTOForUiClientsToDeploy = optimusDeploymentActionsService
					.createOptimusDeploymentServiceActionDTOWithoutPRs(uiClientToDeploy,
							new ArrayList<>(List.of(OptimusDeploymentAction.DEPLOY)));
			optimusDeploymentActionsDTO.getServicesWithoutPr().addAll(odActionsDTOForUiClientsToDeploy.getServicesWithoutPr());
		}
		if(!CollectionUtils.isEmpty(mavenOdServices)) {
			OptimusDeploymentActionsDTO optimusDeploymentActionsDTOForDockerComposeServices = optimusDeploymentActionsService
					.createOptimusDeploymentServiceActionDTOWithoutPRs(mavenOdServices,
							new ArrayList<>(List.of(OptimusDeploymentAction.DEPLOY)));
			optimusDeploymentActionsDTO.getServicesWithoutPr().addAll(optimusDeploymentActionsDTOForDockerComposeServices.getServicesWithoutPr());
		}
		try {
			buildAndDeployService.buildAndDeploy(optimusDeployment.getUuid(), optimusDeploymentActionsDTO, null,
					"default", false);
		} catch (Exception e) {
			log.error("Exception encountered while sending services for Build And Deploy ", e);
		}
	}

	private String createJiraForMasterDeploymentOnQa(){
		String currentDate = DateFormatter.getCurrentDateInIST("MM/dd/yyyy");
		String jiraDescription = "";
		jiraDescription += "*Deployment of master versions of all services on QA* " ;
		String jiraTicket = jiraService.createIssue("MYK", "Deployment of master versions of all services on QA " + currentDate, jiraDescription,
				3L, "MASTER_DEPLOYMENT_QA");
		log.info("Jira ticket created for Canary To Stable Migration {} ", jiraTicket);
		return jiraTicket;
	}

	private OptimusDeployment computeODForMasterMigrationJob(String optimusDeploymentUuid, String jiraTicket) throws URISyntaxException, IOException {
		OptimusDeployment optimusDeployment = new OptimusDeployment();
		optimusDeployment.setDescription("Master Migration on QA");
		optimusDeployment.setCreatorUuid("Optimus NextGen");
		optimusDeployment.setIsValid(true);
		optimusDeployment.setTeam("EngProd");
		optimusDeployment.setTicket(jiraTicket);
		optimusDeployment.setStatus(OptimusDeploymentStatus.DRAFT);
		optimusDeployment.setUuid(optimusDeploymentUuid);
		optimusDeployment.setServices(new ArrayList<>());
		optimusDeployment.setType(optimusDeploymentTypeRepository.findByName(OptimusDeploymentTypeEnum.MASTER_MIGRATION_QA));

		// Compute start time
		Instant instant = Instant.now();
		Timestamp startTime = Timestamp.from(instant);
		log.info("Saving start time for optimusDeploymentUuid " + optimusDeployment.getUuid() + " in DB " + startTime);
		optimusDeployment.setStartTime(startTime);

		computeAndSendNotificationForQaMigrationDeployment(optimusDeployment);
		return optimusDeployment;
	}

	private void computeAndSendNotificationForQaMigrationDeployment(OptimusDeployment optimusDeployment) throws URISyntaxException, IOException {
		TeamResponseDTO teamResponseDTO = paniPuriService.getTeamWebhookUrl(TEAM);
		deploymentNotificationService.computeDefaultSubscriptionAndSaveOptimusDeploymentSubscription(optimusDeployment,
				null, null);
		OptimusDeploymentSubscription optimusDeploymentSubscription = optimusDeploymentSubscriptionRepository.findByOptimusDeploymentUuid(optimusDeployment.getUuid());

		String message = notificationMessageHelper.getMessageForQaMasterMigration();
		Boolean disableDeploymentLink = false;
		String jsonResponse = deploymentNotificationService.initiateOptimusDeploymentNotifications(optimusDeployment, null, 1, optimusDeploymentSubscription, message, disableDeploymentLink);
	}

	private OptimusDeploymentServiceEntity createOdServiceEntityForQAMigration(ServiceEntity service){
		OptimusDeploymentServiceEntity odService = new OptimusDeploymentServiceEntity();
		GHRepository githubRepository = githubService.getRepository(service.getRepositoryName());
		String versionFilePath = githubService.getVersionFilePath(service);
		// Parent Version is the version of the stable branch which will be used for revert
		GHContent parentVersionFileContent = githubService.getFileFromGithub(githubRepository,
				service.getBaseBranch(), versionFilePath);
		String parentVersion = parentVersionFileContent == null ? null
				: githubService.readVersionFromGithubFile(service.getType(), parentVersionFileContent);
		log.info("ParentVersion is {} for service {}", parentVersion, service.getName());
		String issueVersion = parentVersion;
		String issueBranch = service.getBaseBranch();
		if(service.getType().equals(ServiceType.RELEASE_TAG) || service.getType().equals(ServiceType.UI_CLIENT)){
			if(service.getCanaryStatus().equals(CanaryStatus.CANARY)){
				issueBranch = QA_MIGRATION_BRANCH_CANARY;
			}
			else{
				issueBranch = QA_MIGRATION_BRANCH;
			}
			issueVersion = issueVersion + "-QA";
			githubService.updateVersionInGithubVersionFile(service.getRepositoryName(), versionFilePath, service.getType(),
					issueBranch, issueVersion);
			log.info("Issue version is {} for issue branch {} for service {}", issueVersion, issueBranch, service.getName());
		}
		odService.setParentBranch(service.getBaseBranch());
		odService.setParentBranchVersion(parentVersion);
		odService.setIssueBranch(issueBranch);
		odService.setIssueBranchVersion(issueVersion);
		odService.setStatus(OptimusDeploymentServiceStatus.CREATED);
		odService.setUuid(CommonUtils.getGuid());
		odService.setService(service);
		return odService;
	}

	private List<OptimusDeploymentServiceEntity> getOdServicesListForUiClients( List<ServiceEntity> uiClientServices, OptimusDeployment optimusDeployment){
		List<OptimusDeploymentServiceEntity> odServices = new ArrayList<>();
		for(ServiceEntity service : uiClientServices){
			if(!service.getName().equals(KAARMA_CONFIG_TAG_SERVICE) && !service.getName().equals(TOMCAT)) {
				OptimusDeploymentServiceEntity odService = createOdServiceEntityForQAMigration(service);
				odService.setOptimusDeployment(optimusDeployment);
				optimusDeployment.getServices().add(odService);
				odServices.add(odService);
			}
		}

		return odServices;

	}
	private void createMasterMigrationBranch(Map<String, List<ServiceEntity>> repoToServiceMap){
		repoToServiceMap.forEach((repo, services) -> {
			boolean isBranchForCanaryServicesCreated = false;
			boolean isBranchForServicesCreated = false;
			for(ServiceEntity service : repoToServiceMap.get(repo)){
				String baseBranch = service.getBaseBranch();
				if(isBranchForServicesCreated && isBranchForCanaryServicesCreated){
					break;
				}
				if(service.getCanaryStatus().equals(CanaryStatus.CANARY) && !isBranchForCanaryServicesCreated){
					githubService.deleteBranchIfExists(service.getRepositoryName(), QA_MIGRATION_BRANCH_CANARY);
					try {
						githubService.createBranch(service.getRepositoryName(), QA_MIGRATION_BRANCH_CANARY, baseBranch);
						isBranchForCanaryServicesCreated = true;
					} catch (IOException e) {
						log.error("Error encountered while creating branch {} from base branch {} for repo {} ",
								QA_MIGRATION_BRANCH_CANARY, baseBranch, service.getRepositoryName(), e);
					}
				}
				else if (!service.getName().equals(KAARMA_CONFIG_TAG_SERVICE) && !service.getName().equals(TOMCAT) && !isBranchForServicesCreated) {
					githubService.deleteBranchIfExists(service.getRepositoryName(), QA_MIGRATION_BRANCH);
					try {
						githubService.createBranch(service.getRepositoryName(), QA_MIGRATION_BRANCH, baseBranch);
						isBranchForServicesCreated = true;
					} catch (IOException e) {
						log.error("Error encountered while creating branch {} from base branch {} for repo {} ",
								QA_MIGRATION_BRANCH, baseBranch, service.getRepositoryName(), e);
					}
				}
			}
		});
	}
	private List<OptimusDeploymentServiceEntity> getOdServicesListForMavenServices( List<ServiceEntity> mavenServices, OptimusDeployment optimusDeployment){
		List<OptimusDeploymentServiceEntity> odServices = new ArrayList<>();

		for (ServiceEntity service: mavenServices){
			if(service.getName().equals("optimus-nextgen--server")) continue ;
			OptimusDeploymentServiceEntity odService = createOdServiceEntityForQAMigration(service);
			odService.setOptimusDeployment(optimusDeployment);
			optimusDeployment.getServices().add(odService);
			odServices.add(odService);
		}
		return odServices;

	}

	private HashMap<String, String> getServiceVersionMapFromEnvFile(String environment) {
		HashMap<String, String> serviceVersionMap = new HashMap<>() ;
		String content;
		List<Object[]> deploymentTargets = deploymentTargetRepository.findServiceNameAndDeploymentTargets(Arrays.asList(ServiceType.UI_CLIENT, ServiceType.RELEASE_TAG, ServiceType.MAVEN_DEPLOYMENT));
		GHRepository envFileRepository = githubService.getRepository(ENV_FILE_REPO);
		HashMap<String, String> processedEnvFiles = new HashMap<>();

		for(Object[] row: deploymentTargets) {
			String serviceName = (String) row[0];
			DeploymentTarget deploymentTarget = (DeploymentTarget) row[1];
			String envFilePath = deploymentTarget.getEnvFilePath();

			if(environment.equals(PROD_ENVIRONMENT)){
				envFilePath = envFilePath.replace("qa-aws-infra", "prod-aws-infra");
				envFilePath = envFilePath.replace("qa-aws", "prod");

				if(envFilePath.contains("docker-compose")) {
					if(serviceName.equals("mk-login-saml") ||
					   serviceName.equals("kaarma-admin-10")) {
						envFilePath = "/docker-compose/prod/docker06/.env";
					} else if(serviceName.equals("DMS-Integration-Webservice")) {
						envFilePath = "/docker-compose/prod/dms/.env";
					}
				}
			}
			if(processedEnvFiles.containsKey(envFilePath)) {
				continue;
			}

			GHContent envFileContent = githubService.getFileFromGithub(envFileRepository, ENV_FILE_BRANCH, envFilePath);

			try (InputStream inputStream = envFileContent.read()) {
				content = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
				processedEnvFiles.put(envFilePath, content);
			} catch (Exception e) {
				log.error("Unable to parse EnvFile for envFile {} for service {}" , envFilePath, serviceName);
				continue ;
			}
			serviceVersionMap.putAll(getVersionFromContent(content));
		}
		return serviceVersionMap ;
	}

	private HashMap<String, String> getVersionFromContent(String content){
		HashMap<String, String> serviceVersionMap = new HashMap<>() ;
		List<List<String>> allMatches = RegexUtils.getAllGroupsForAllMatches(content, ENV_FILE_PARSING_REGEX);
		for(List<String> matches : allMatches) {
			matches.set(1,matches.get(1).replace(QA_SUFFIX, ""));
			serviceVersionMap.put(matches.get(0), matches.get(1));
		}
		return serviceVersionMap ;
	}

	public List<String> getServiceEnvListThatNeedsToBeMigrated() {
		HashMap<String,String> qaServiceVersionMap = getServiceVersionMapFromEnvFile(STAGING_ENVIRONMENT);
		HashMap<String,String> prodServiceVersionMap = getServiceVersionMapFromEnvFile(PROD_ENVIRONMENT) ;
		List<String> serviceEnvListThatNeedsToBeBuilt = new ArrayList<>() ;
		for(String key : qaServiceVersionMap.keySet()){
			log.info("Value of key is: {} , qaServiceVersionMap is: {} , prodServiceVersionMap is: {}", key, qaServiceVersionMap.get(key), prodServiceVersionMap.get(key));
			if(prodServiceVersionMap.get(key) != null && !prodServiceVersionMap.get(key).equals(qaServiceVersionMap.get(key))){
				log.info("Keys with different version is {} and value of Prod is {} and value of QA is {}" , key, prodServiceVersionMap.get(key), qaServiceVersionMap.get(key));
				serviceEnvListThatNeedsToBeBuilt.add(key) ;
			}
			if(prodServiceVersionMap.get(key) == null){
				log.info("Key is not present in prod: {} and value is: {}" , key, prodServiceVersionMap.get(key));
			}
		}
		return serviceEnvListThatNeedsToBeBuilt ;
	}

	public void runMasterMigration(List<String> serviceEnvNameList) {
		//Initialising Required Objects
		List<OptimusDeploymentServiceEntity> odServices = new ArrayList<>();
		OptimusDeployment optimusDeployment ;
		Map<String, List<ServiceEntity>> repoToServiceMap;
		List<OptimusDeploymentServiceEntity> serviceEntityWithTypeClientAndReleaseTagNeedsToBeBuilt;
		List<OptimusDeploymentServiceEntity> serviceEntityWithTypeMavenNeedsToBeBuilt;
		List<OptimusDeploymentServiceEntity> servicesEntityWithTypeClientAndReleaseTagAndNoDeploymentTargets;

		// Fetching Service Entities
		List<ServiceEntity> serviceEntityListNeedsToBeMigrated = deploymentTargetRepository.findServiceEntityByServiceEnvName(serviceEnvNameList) ;
		List<ServiceEntity> servicesWithTypeClientAndReleaseTagAndNoDeploymentTargets = serviceRepository.findByTypeAndNoDeploymentTarget(Arrays.asList(ServiceType.UI_CLIENT, ServiceType.RELEASE_TAG));
		List<ServiceEntity> serviceWithTypeClientAndReleaseTagAndNullEnvFilePath = serviceRepository.findServiceEntityByTypeAndEnvFilePathIsNull(Arrays.asList(ServiceType.UI_CLIENT, ServiceType.RELEASE_TAG));
		List<ServiceEntity> dockerComposeServices = serviceRepository.findServicesByTypeAndNotLikeEnvPath(ServiceType.MAVEN_DEPLOYMENT, ENV_FILE_PATH);

		// Separating UI_Clients and Release Tags from Maven Deployments.
		List<ServiceEntity> serviceWithTypeClientAndReleaseTagNeedsToBeMigrated = new ArrayList<>();
		List<ServiceEntity> serviceWithTypeMavenNeedsToBeMigrated = new ArrayList<>() ;
		for(ServiceEntity serviceEntity : serviceEntityListNeedsToBeMigrated){
			if(serviceEntity.getType() == ServiceType.MAVEN_DEPLOYMENT){
				serviceWithTypeMavenNeedsToBeMigrated.add(serviceEntity) ;
			}
			else{
				serviceWithTypeClientAndReleaseTagNeedsToBeMigrated.add(serviceEntity) ;
			}
		}

		// Combining Same service Types.
		serviceWithTypeClientAndReleaseTagNeedsToBeMigrated.addAll(serviceWithTypeClientAndReleaseTagAndNullEnvFilePath) ;
		serviceWithTypeMavenNeedsToBeMigrated.addAll(dockerComposeServices) ;

		// Creating Optimus Deployment
		try {
			String optimusDeploymentUuid = CommonUtils.getGuid();
			String jiraTicket = createJiraForMasterDeploymentOnQa();
			optimusDeployment = computeODForMasterMigrationJob(optimusDeploymentUuid, jiraTicket);
		} catch (Exception e){
			log.error("Optimus Deployment could not be created ", e);
			return;
		}


		// Computing Optimus Deployment Service From Service Entities Of UI_CLIENT AND RELEASE_TAG.
		repoToServiceMap = computeRepoToServiceMap(Stream.concat(servicesWithTypeClientAndReleaseTagAndNoDeploymentTargets.stream(), serviceWithTypeClientAndReleaseTagNeedsToBeMigrated.stream()).collect(Collectors.toList()));
		createMasterMigrationBranch(repoToServiceMap);
		servicesEntityWithTypeClientAndReleaseTagAndNoDeploymentTargets = getOdServicesListForUiClients(servicesWithTypeClientAndReleaseTagAndNoDeploymentTargets, optimusDeployment) ;
		servicesEntityWithTypeClientAndReleaseTagAndNoDeploymentTargets = removeServicesAlreadyBuiltAndNoDeploymentTargets(servicesEntityWithTypeClientAndReleaseTagAndNoDeploymentTargets) ;
		serviceEntityWithTypeClientAndReleaseTagNeedsToBeBuilt = getOdServicesListForUiClients(serviceWithTypeClientAndReleaseTagNeedsToBeMigrated, optimusDeployment) ;
		serviceEntityWithTypeClientAndReleaseTagNeedsToBeBuilt.addAll(servicesEntityWithTypeClientAndReleaseTagAndNoDeploymentTargets) ;

		// Computing Optimus Deployment Service From Service Entities Of MAVEN_DEPLOYMENT.
		serviceEntityWithTypeMavenNeedsToBeBuilt = getOdServicesListForMavenServices(serviceWithTypeMavenNeedsToBeMigrated, optimusDeployment) ;

		// Adding All Optimus Deployment Service Entities to Optimus Deployment And Saving the Deployment in the DB.
		odServices.addAll(serviceEntityWithTypeClientAndReleaseTagNeedsToBeBuilt);
		odServices.addAll(serviceEntityWithTypeMavenNeedsToBeBuilt);
		optimusDeployment.setServices(odServices);
		odRepository.save(optimusDeployment);

		sendUiClientServicesAndMavenServicesForBuildAndDeploy(optimusDeployment, serviceEntityWithTypeMavenNeedsToBeBuilt, serviceEntityWithTypeClientAndReleaseTagNeedsToBeBuilt);
	}

	private List<OptimusDeploymentServiceEntity> removeServicesAlreadyBuiltAndNoDeploymentTargets(List<OptimusDeploymentServiceEntity> servicesWithTypeClientAndReleaseTagAndNoDeploymentTargets){
		List<OptimusDeploymentServiceEntity> odServiceNeedToRemove = new ArrayList<>() ;
		List<String> servicesNeedToBeRemoved = new ArrayList<>() ;
		if(servicesWithTypeClientAndReleaseTagAndNoDeploymentTargets != null){
			for(OptimusDeploymentServiceEntity odService : servicesWithTypeClientAndReleaseTagAndNoDeploymentTargets){
				if(!StringUtils.isEmpty(odService.getService().getDockerImage())){
					log.info("Service name {} and docker Image {}" , odService.getService().getName() , odService.getService().getDockerImage());
					if(awsUtils.isImageExistsInECR(registryId, odService.getService().getDockerImage(), odService.getIssueBranchVersion())){
						servicesNeedToBeRemoved.add(odService.getService().getName());
						odServiceNeedToRemove.add(odService) ;
					}
				}
			}
		}
		log.info("List of services need to be removed: {}" , servicesNeedToBeRemoved) ;
		servicesWithTypeClientAndReleaseTagAndNoDeploymentTargets.removeAll(odServiceNeedToRemove) ;
		return servicesWithTypeClientAndReleaseTagAndNoDeploymentTargets ;
	}
	private Boolean hasValidDeploymentTargets(List<DeploymentTarget> deploymentTargets){
		Boolean hasValidDeploymentTargets = false ;
		for(DeploymentTarget deploymentTarget : deploymentTargets){
			if(deploymentTarget.getIsValid().equals(true)) hasValidDeploymentTargets = true ; break;
		}
		return hasValidDeploymentTargets ;
	}


}
