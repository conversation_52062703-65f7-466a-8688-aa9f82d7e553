package com.mykaarma.optimus.utils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpression;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mykaarma.optimus.configs.RateLimitHandler;
import com.mykaarma.optimus.jpa.model.OptimusDeployment;
import com.mykaarma.optimus.jpa.model.OptimusDeploymentServiceEntity;
import com.mykaarma.optimus.model.dto.DependencyDetails;
import com.mykaarma.optimus.model.dto.ErrorDTO;
import com.mykaarma.optimus.model.dto.CompatibilityCheckDTO;
import com.mykaarma.optimus.model.enums.Constants;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.kohsuke.github.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import com.mykaarma.optimus.exception.BadArgumentsException;
import com.mykaarma.optimus.jpa.model.ServiceEntity;
import com.mykaarma.optimus.model.dto.PomContentDTO;
import com.mykaarma.optimus.model.enums.ErrorCodes;
import com.mykaarma.optimus.model.enums.ServiceType;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class GithubService {

//	private static final String HAPROXY_VERSION_FILE_REGEX = "([^\\/]+\\/[^\\/]+)\\/.*";
	private static final String HAPROXY_VERSION_FILE_REGEX = "(HAProxy\\/(QA|PROD))\\/.*";
	private static final String PR_INFO_EXTRACTOR_REGEX = "https:\\/\\/github\\.(?:com|dev)\\/(mykaarma\\/[^\\/]+)\\/pull\\/(\\d+)(\\/|\\/files)?\\/?$";
	private static final String FILE_INFO_EXTRACTOR_REGEX = "^https:\\/\\/github\\.com\\/([^\\/]+)\\/([^\\/]+)\\/blob\\/([^\\/]+)\\/" ;

	private static  final String POM_XML_OR_VERSION_TXT_PATH_REGEX_PATTERN = "^https:\\/\\/github\\.(?:com|dev)\\/(mykaarma\\/[A-Za-z0-9_-]+)\\/blob\\/([A-Za-z0-xw9_-]+)(((?:\\/[^\\/]+)*\\/)(?:pom\\.xml|version\\.txt|package\\.json))\\/?$";

	private static  final String DEPENDENCY_PATH = "^https:\\/\\/github\\.(?:com|dev)\\/(mykaarma\\/[A-Za-z0-9_-]+)\\/blob\\/([A-Za-z0-xw9_-]+)(((?:\\/[^\\/]+)*\\/)(?:pom\\.xml|requirements\\.txt|package\\.json))\\/?$";
	private GitHub github;
	private static final String ARTIFACT_ID = "artifactId";
	private static final String VERSION = "version";
	private static final String DEPENDENCY = "dependency";
	private final List<String> allGithubTokens;
	private final List<String> validGithubTokens = new ArrayList<>();
	private int tokenIndex = 0;
	private static final String GRAPHQL_URL = "https://api.github.com/graphql";
	private static final String REPO_OWNER = "mykaarma";
	

	@Autowired
	public GithubService(
			@Value("${github.access.token1}") String accessToken1,
			@Value("${github.access.token2}") String accessToken2,
			@Value("${github.access.token3}") String accessToken3) throws IOException {
		this.allGithubTokens = List.of(accessToken1, accessToken2, accessToken3)
				.stream()
				.filter(token -> token != null && !token.isBlank())
				.toList();

		if (allGithubTokens.isEmpty()) {
			throw new IllegalArgumentException("No valid GitHub tokens provided in the configuration.");
		}

		refreshValidTokensList();
	}

	/**
	 * Refresh the list of valid tokens.
	 */
	public synchronized void refreshValidTokensList() throws IOException {
		List<String> tempGithubTokens = new ArrayList<>();
		for (String token : allGithubTokens) {
			try {
				GitHubRateLimitHandler handler = new RateLimitHandler();
				GHRateLimit rateLimit = new GitHubBuilder().withRateLimitHandler(handler).withOAuthToken(token).build().getRateLimit();
				log.info("GitHub object created for token: {}", token);
				if (rateLimit.getRemaining() > 0) {
					tempGithubTokens.add(token);
					log.info("Token added to valid list: {} with remaining rate limit: {}", token, rateLimit.getRemaining());
				} else {
					log.warn("Token is invalid or rate limit exceeded for token: {}. Remaining: {}", token, rateLimit.getRemaining());
				}
			} catch (Exception e) {
				log.error("Unexpected error while validating token: {}", token, e);
			}
		}

		if (tempGithubTokens.isEmpty()) {
			throw new IOException("No valid GitHub tokens available.");
		}

		validGithubTokens.clear();
		validGithubTokens.addAll(tempGithubTokens);

		log.info("Valid tokens refreshed. Count: {}", validGithubTokens.size());
		tokenIndex = 0;
	}
	
	/**
	 * Get a GitHub client using tokens in a round-robin manner.
	 */
	public synchronized GitHub getGitHubClient() throws IOException {
		if (validGithubTokens.isEmpty()) {
			log.warn("No valid tokens available. Attempting to refresh token list.");
			refreshValidTokensList();
		}
		String token = validGithubTokens.get(tokenIndex);
		github = new GitHubBuilder().withOAuthToken(token).build();
		log.info("GitHub client initialized with token: {}", token);

		tokenIndex = (tokenIndex + 1) % validGithubTokens.size();
		return github;
	}

	/**
	 * Get a GitHub token using tokens in a round-robin manner for graphql query.
	 */
	private String getGitHubToken() throws IOException {
		if (validGithubTokens.isEmpty()) {
			throw new IOException("No valid GitHub tokens available.");
		}

		String token = validGithubTokens.get(tokenIndex);
		tokenIndex = (tokenIndex + 1) % validGithubTokens.size();
		return token;
	}


	public List<String> fetchFilesFromGithub(String repositoryName, String directoryPath, String refBranch)
			throws IOException {
		List<GHContent> directoryContent = getGitHubClient().getRepository(repositoryName).getDirectoryContent(directoryPath,
				refBranch);
		List<String> filesDownloadUrlList = new ArrayList<>();
		for (GHContent files : directoryContent) {
			log.info("Adding file " + files.getName() + " in files list");
			filesDownloadUrlList.add(files.getDownloadUrl());
		}
		return filesDownloadUrlList;
	}

	public boolean isValidPomXmlOrVersionTxtUrl(String pomUrl){
		try{
			if (RegexUtils.isMatched(pomUrl, POM_XML_OR_VERSION_TXT_PATH_REGEX_PATTERN)) {
				log.info("Url format is valid");
				return true;
			}
		} catch (Exception e){
			log.error("Unable to process path URL: " + pomUrl, e);
			throw new BadArgumentsException(ErrorCodes.BAD_POM_XML_OR_VERSION_TXT_PATH, "Expected path url should match the regex: " + POM_XML_OR_VERSION_TXT_PATH_REGEX_PATTERN);
		}

		log.error("Path url format is invalid, please provide a valid url matching the regex: " + POM_XML_OR_VERSION_TXT_PATH_REGEX_PATTERN);
		return false;
	}

	public List<String> getRepoNameAndPrNumber(String prUrl) {
		List<String> processedPrDetails = new ArrayList<>();
		try {
			List<String> allMatches = RegexUtils.getFirstMatchedGroupsWithinRegex(prUrl, PR_INFO_EXTRACTOR_REGEX);
			processedPrDetails.add(allMatches.get(0));
			processedPrDetails.add(allMatches.get(1));
			log.info("PR repo name: " + processedPrDetails.get(0) + " PR Number: " + processedPrDetails.get(1));
		} catch (Exception e) {
			log.error("Unable to process PR URL: " + prUrl, e);
			throw new BadArgumentsException(ErrorCodes.BAD_PR_LINK, "PR Link is not in proper format.");
		}
		return processedPrDetails;
	}

	public List<String> extractDetailsFromPomXmlOrVersionTxtUrl(String pomXmlOrVersionTxtUrl){
		List<String> extractedDetails;
		try{
			log.info("Processing pom.xml or version.txt url to extract repository name, branch and relative folder of the file and folder name inside the repository");
			extractedDetails = new ArrayList<>(RegexUtils.getFirstMatchedGroupsWithinRegex(pomXmlOrVersionTxtUrl, POM_XML_OR_VERSION_TXT_PATH_REGEX_PATTERN));
		} catch (Exception e){
			log.error("Unable to process url: " + pomXmlOrVersionTxtUrl, e);
			throw new BadArgumentsException(ErrorCodes.BAD_POM_XML_OR_VERSION_TXT_PATH, "Cannot extract required details from the pom path provided");
		}

		return extractedDetails;
	}

	public List<String> extractDetailsFromDependencyFilePathUrl(String dependencyFilePathUrl){
		List<String> extractedDetails = List.of();
		try{
			log.info("Processing pom.xml or version.txt url to extract repository name, branch and relative folder of the file and folder name inside the repository");
			extractedDetails = new ArrayList<>(RegexUtils.getFirstMatchedGroupsWithinRegex(dependencyFilePathUrl, DEPENDENCY_PATH));
		} catch (Exception e){
			log.error("Unable to process url: " + dependencyFilePathUrl, e);
//			throw new BadArgumentsException(ErrorCodes.BAD_POM_XML_OR_VERSION_TXT_PATH, "Cannot extract required details from the pom path provided");
		}

		return extractedDetails;
	}




	public Boolean mergePR(String repoName, int prNumber, String userName, String ticket, String branchName)
	{
		try {
			GHRepository githubRepository = getGitHubClient().getRepository(repoName);
			GHPullRequest pullRequest = githubRepository.getPullRequest(prNumber);
			log.info("Merging PR of repo " + repoName + " and PR number " + prNumber );
			pullRequest.comment(userName + " merged pull request using Optimus-NextGen for Jira " + ticket);
			pullRequest.merge(userName + " merged pull request using Optimus-NextGen for Jira " + ticket);
			boolean isPRMerged = checkIfPrAlreadyMerged(repoName, prNumber);
			return isPRMerged;
		}
		catch(Exception e)
		{
			log.error("Unable to merge PR of repo " + repoName + " and PR number " + prNumber + " " + e.getMessage(), e);
			return false;
		}
	}

	public GHPullRequest getPullRequest(String prLink) throws IOException {
		List<String> processedPrDetails = getRepoNameAndPrNumber(prLink);
		String repoName = processedPrDetails.get(0);
		int prNumber = Integer.parseInt(processedPrDetails.get(1));

		return getPullRequest(repoName, prNumber);
	}

	public GHPullRequest getPullRequest(String repoName, Integer prNumber) throws IOException {
			return getGitHubClient().getRepository(repoName).getPullRequest(prNumber);
	}

	// TODO get rid of this method and use ghPullRequest.listFiles() when wiremock
	// is in place for testing
	public List<GHPullRequestFileDetail> getFilesInPr(GHPullRequest ghPullRequest) {
		List<GHPullRequestFileDetail> files = new ArrayList<>();
		for (GHPullRequestFileDetail fileDetail : ghPullRequest.listFiles()) {
			files.add(fileDetail);
		}
		return files;
	}

	public GHContent getFileFromGithub(GHRepository githubRepository, String branchName, String filePath) {
		try {
			return githubRepository.getFileContent(filePath, branchName);
		} catch (IOException e) {
			log.warn("Unable to fetch file contents from github. repo={}, branch={}, file={}",
					githubRepository.getName(), branchName, filePath);
			return null;
		}
	}

	public String getFileFromGithub(String githubURL) throws IOException{
		List<String> allMatches = RegexUtils.getFirstMatchedGroupsWithinRegex(githubURL, FILE_INFO_EXTRACTOR_REGEX);
			String[] parts = githubURL.split("/");
			String repoOwner = allMatches.get(0);
			String repoName = allMatches.get(1);
			String branch = allMatches.get(2);
			String filePath = String.join("/", parts).replace("https://github.com/", "").replace(repoOwner + "/" + repoName + "/blob/" + branch + "/", "");
			GHRepository repo = getGitHubClient().getRepository(repoOwner + "/" + repoName);
			GHContent content = repo.getFileContent(filePath, branch);
			return content.getContent() ;
	}

	public String getRepositoryNameFromGithubURL(String githubURL) {
		String repoName = null ;
		try {
			List<String> allMatches = RegexUtils.getFirstMatchedGroupsWithinRegex(githubURL, FILE_INFO_EXTRACTOR_REGEX);
			repoName = allMatches.get(1);
		} catch (Exception e){
			log.error("Unable to find Repository Name from the given Github URL", e);
		}
		return repoName ;
	}

	public boolean isReleaseTagCreated(String repoName, String tag) throws IOException {
		log.info("Checking if Release tag " + tag + " is already created For repo : " + repoName);
		GHRelease releaseByTagName = getGitHubClient().getRepository(repoName).getReleaseByTagName(tag);
		if (Optional.ofNullable(releaseByTagName).isPresent()) {
			log.info("Release tag " + tag + " is already created For repo : " + repoName);
			return true;
		} else {
			log.info("Release tag " + tag + " is not created For repo : " + repoName);
			return false;
		}
	}

	public boolean createReleaseTag(String repoName, String tag, String branch) {
		boolean isReleaseTagCreated = false;
		try {
			isReleaseTagCreated = isReleaseTagCreated(repoName, tag);
			if (!isReleaseTagCreated) {
				log.info("Creating Release tag : " + tag + " for Repo : " + repoName);
				getGitHubClient().getRepository(repoName).createRelease(tag).prerelease(false).draft(false).commitish(branch)
						.create();
				isReleaseTagCreated = isReleaseTagCreated(repoName, tag);
				log.info("Successfully created Release tag : " + tag + " for Repo : " + repoName);
			}
		} catch (IOException e) {
			log.error("Unable to create release tag : " + tag + " for repo : " + repoName, e);
		}
		return isReleaseTagCreated;
	}

	public GHRepository getRepository(String repositoryName) {
		try {
			return getGitHubClient().getRepository(repositoryName);
		} catch (Exception e) {
			throw new BadArgumentsException(ErrorCodes.GITHUB_REPOSITORY_NOT_FOUND, "Cannot find repository: " + repositoryName);
		}
	}

	public String getVersionFilePath(ServiceEntity service) {
		String versionFilePath = null;
		if(ServiceType.MAVEN_JAR.equals(service.getType()) || ServiceType.MAVEN_DEPLOYMENT.equals(service.getType()) ) {
			versionFilePath = service.getFolderPath() + "pom.xml";
		} else if (ServiceType.RELEASE_TAG.equals(service.getType()) || ServiceType.UI_CLIENT.equals(service.getType())) {
			versionFilePath = service.getFolderPath() + "version.txt" ;
		} else if(ServiceType.NPM_PACKAGE.equals(service.getType())){
			versionFilePath = service.getFolderPath() + "package.json" ;
		}
		else if (ServiceType.HAPROXY.equals(service.getType())) {
			log.info("Checking for version file path using regex pattern");
			if(RegexUtils.isMatched(service.getFolderPath(), HAPROXY_VERSION_FILE_REGEX)){
				List<String> extractedDetails = new ArrayList<>(RegexUtils.getFirstMatchedGroupsWithinRegex(service.getFolderPath(), HAPROXY_VERSION_FILE_REGEX));
				versionFilePath = extractedDetails.get(0) + "/version.txt";
				log.info("regex pattern matched for haproxy version file, setting version file path to: {}", versionFilePath);
			}
		}
		return versionFilePath;
	}

	public String getDependencyPath(ServiceEntity service){
		String dependencyFilePath = service.getDependencyFilePath();
		return dependencyFilePath;
	}

	public CompatibilityCheckDTO checkCommitsInBlackBoxFolder(String internalSystemPR , String deploymentProfile) throws IOException{
		List<String> prDetails = getRepoNameAndPrNumber(internalSystemPR) ;
		GHRepository repository = getGitHubClient().getRepository(prDetails.get(0));
		GHPullRequest pullRequest = repository.getPullRequest(Integer.parseInt(prDetails.get(1)));

		CompatibilityCheckDTO compatibilityCheck = new CompatibilityCheckDTO();
		compatibilityCheck.setValid(false);
		boolean hasCommits = false ;

		if(deploymentProfile.equalsIgnoreCase(Constants.PROD.getKey())){
			for (GHPullRequestFileDetail file : pullRequest.listFiles()) {
				if(file.getFilename().contains(Constants.BLACKBOX_EXPORTER.getKey()) && file.getFilename().contains(Constants.ENDPOINTS.getKey())){
					hasCommits = true ;
					break ;
				}
			}
		}
		else {
			for (GHPullRequestFileDetail file : pullRequest.listFiles()) {
				if(file.getFilename().contains(Constants.BLACKBOX_EXPORTER.getKey()) && file.getFilename().contains(Constants.QA_ENDPOINTS.getKey())){
					hasCommits = true ;
					break ;
				}
			}
		}
		if(!hasCommits){
			compatibilityCheck.setValidationErrors(new ErrorDTO(ErrorCodes.NO_HEALTH_CHECK_IN_BLACK_BOX));
			return compatibilityCheck;
		}

		compatibilityCheck.setValid(true);
		return compatibilityCheck;
	}
	public boolean checkCommitsInPullRequest(String prUrl , ArrayList<String> filePathsToCheckCommits) throws IOException{
		List<String> prDetails = getRepoNameAndPrNumber(prUrl) ;
		GHRepository repository = getGitHubClient().getRepository(prDetails.get(0));
		GHPullRequest pullRequest = repository.getPullRequest(Integer.parseInt(prDetails.get(1)));
		boolean hasCommits;
		for(String checkFile : filePathsToCheckCommits) {
			hasCommits = false ;
			for (GHPullRequestFileDetail file : pullRequest.listFiles()) {
				if(file.getFilename().equalsIgnoreCase(checkFile)){
					hasCommits = true ;
					break ;
				}
			}
			if(!hasCommits){
				return false;
			}
		}

		return true;
	}

	public String readVersionFromGithubFile(ServiceType serviceType, GHContent versionFileContent) {
		try (InputStream stream = versionFileContent.read()) {
			if(ServiceType.MAVEN_DEPLOYMENT.equals(serviceType) || ServiceType.MAVEN_JAR.equals(serviceType)) {
				DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
				DocumentBuilder db = dbf.newDocumentBuilder();
				Document xmlDocument  = db.parse(stream);

				XPathFactory xpf = XPathFactory.newInstance();
				XPath xp = xpf.newXPath();
				return xp.evaluate("/project/version/text()", xmlDocument.getDocumentElement());
			} else if (ServiceType.RELEASE_TAG.equals(serviceType) || ServiceType.HAPROXY.equals(serviceType) || ServiceType.UI_CLIENT.equals(serviceType)) {
				String content = new String(stream.readAllBytes(), StandardCharsets.UTF_8);
				return StringUtils.substringAfterLast( content, "=" ).trim();
			} else if(ServiceType.NPM_PACKAGE.equals(serviceType)){
				String content = new String(stream.readAllBytes(), StandardCharsets.UTF_8);
				JSONObject jsonObject = new JSONObject(content) ;
				return jsonObject.getString("version");
			}
		} catch (IOException | ParserConfigurationException | SAXException |
						 XPathExpressionException e) {
			log.error("Unable to read version from github. versionFile={}", versionFileContent.getName(), e);
			return null;
		}
		return null;
	}

	public PomContentDTO readPomDetailsFromGithub(GHContent pomContent) {
		PomContentDTO pomContentDTO = new PomContentDTO();
		try (InputStream stream = pomContent.read()) {
				DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
				DocumentBuilder db = dbf.newDocumentBuilder();
				Document xmlDocument  = db.parse(stream);

				XPathFactory xpf = XPathFactory.newInstance();
				XPath xp = xpf.newXPath();
			  pomContentDTO.setGroupId(xp.evaluate("/project/groupId/text()", xmlDocument.getDocumentElement()));
		   	pomContentDTO.setArtifactId(xp.evaluate("/project/artifactId/text()", xmlDocument.getDocumentElement()));
			  pomContentDTO.setVersion(xp.evaluate("/project/version/text()", xmlDocument.getDocumentElement()));
			return pomContentDTO;
		} catch (IOException | ParserConfigurationException | SAXException |
						 XPathExpressionException e) {
			log.error("Unable to read pom details from github. versionFile={}", pomContent.getName(), e);
			return pomContentDTO;
		}
	}

	
	public boolean checkIfPrAlreadyMerged(String repositoryName, Integer prNumber) {
		boolean isAlreadyMerged = false;
		try {
			GHRepository githubRepository = getGitHubClient().getRepository(repositoryName);
			GHPullRequest pullRequest = githubRepository.getPullRequest(prNumber);
			log.info("Is PR "+prNumber+" already merged : "+pullRequest.isMerged());
			isAlreadyMerged = pullRequest.isMerged();
		}
		catch (Exception e){
			log.info("Unable to check merge status of PR : "+prNumber+", "+e.getMessage(), e);
		}
		return isAlreadyMerged;
	}

	public void addCommentInPR(String repoName, int prNumber, String message){
		try{
			GHRepository githubRepository = getGitHubClient().getRepository(repoName);
			GHPullRequest pullRequest = githubRepository.getPullRequest(prNumber);
			log.info("Commenting PR of repo " + repoName + " and PR number " + prNumber );
			pullRequest.comment(message);
		}
		catch (Exception e){
			log.error("Unable to add Comment on PR of repo " + repoName + " and PR number " + prNumber + " " + e.getMessage(), e);
		}
	}
	
	public void createBranch(String repoName, String newBranch, String baseBranch) throws IOException
	{
		
			GHRepository gHRepo = getGitHubClient().getRepository(repoName);
			gHRepo.createRef("refs/heads/" + newBranch,gHRepo.getBranch(baseBranch).getSHA1());
		
	}
	
	public void deleteBranchIfExists(String repoName, String branch)
	{
		try { 
		GHRef branchRef = getGitHubClient().getRepository(repoName).getRef("heads/"+ branch);
		if(branchRef != null)
		{
			branchRef.delete();
		}
		} catch(IOException e)
		{
			log.error("Error encountered while deleting branch {}  from repo {} ", branch, repoName, e);

		}

	}

	public void updateVersionInGithubVersionFile(String repoName, String versionFilePath, ServiceType serviceType,
			String issueBranch, String newVersion) {
		try {

			GHRepository repository = getGitHubClient().getRepository(repoName);
			GHContent versionFileContent = repository.getFileContent(versionFilePath, issueBranch);
			String currentCommitSha = versionFileContent.getSha();

			try (InputStream stream = versionFileContent.read()) {
				if (ServiceType.MAVEN_DEPLOYMENT.equals(serviceType) || ServiceType.MAVEN_JAR.equals(serviceType)) {
					
					DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
					DocumentBuilder db = dbf.newDocumentBuilder();
					Document xmlDocument  = db.parse(stream);
			        log.info("here " + xmlDocument);
			        XPathFactory xpf = XPathFactory.newInstance();
					XPath xp = xpf.newXPath();
			        XPathExpression expression = xp.compile("/project/version/text()");
			        Node versionNode = (Node) expression.evaluate(xmlDocument, XPathConstants.NODE);

			        // Update the value of the <version> element with the new version
			        versionNode.setNodeValue(newVersion);

			        // Serialize the updated XML document back to a string
			        TransformerFactory transformerFactory = TransformerFactory.newInstance();
			        Transformer transformer = transformerFactory.newTransformer();
			        StringWriter writer = new StringWriter();
			        transformer.transform(new DOMSource(xmlDocument), new StreamResult(writer));
			        String updatedFileContentString = writer.toString();
					repository.createContent().path(versionFilePath).content(updatedFileContentString)
							.message("Update version to " + newVersion).branch(issueBranch).sha(currentCommitSha)
							.commit();

				} else if (ServiceType.RELEASE_TAG.equals(serviceType) || ServiceType.UI_CLIENT.equals(serviceType)) {
					String currentContent = new String(stream.readAllBytes(), StandardCharsets.UTF_8);
					String updatedContent = currentContent.replaceAll("=.*", "=" + newVersion);
					repository.createContent().path(versionFilePath).content(updatedContent)
							.message("Update version to " + newVersion).branch(issueBranch) // Replace with the
																							// appropriate
							.sha(currentCommitSha) // branch // branch
							.commit();
				}
			} catch (IOException | ParserConfigurationException | SAXException  e) {
				log.error("Unable to read version from github. versionFile={}", versionFileContent.getName(), e);
			}
		} catch (
		Exception e) {
			log.error("Error encountered while updating version in github ", e);
		}

	}

	public String getDependenciesFilePath(ServiceType serviceType, String branch, String repoName, String folderPath) throws IOException {
		GHContent content = null;
		try {
			if (serviceType.equals(ServiceType.MAVEN_JAR) || serviceType.equals(ServiceType.MAVEN_DEPLOYMENT)) {
				String dependencyPath = folderPath + "pom.xml";
				GHRepository repo = getGitHubClient().getRepository(repoName);
				content = repo.getFileContent(dependencyPath, branch);
			} else if (serviceType.equals(ServiceType.RELEASE_TAG) || serviceType.equals(ServiceType.UI_CLIENT)) {
				try {
					String dependencyPath = folderPath + "package.json";
					GHRepository repo = getGitHubClient().getRepository(repoName);
					content = repo.getFileContent(dependencyPath, branch);
				} catch(GHFileNotFoundException e){
					try {
						String dependencyPath = folderPath + "requirements.txt";
						log.info("dependencyPath: " + dependencyPath);
						GHRepository repo = getGitHubClient().getRepository(repoName);
						content = repo.getFileContent(dependencyPath, branch);
					} catch(GHFileNotFoundException e2){
						log.error("Dependency file does not exist for the service", e2);
					}
				}
			}
		} catch (Exception e) {
			log.error("Dependency file does not exist for the service", e);
		}
		return content != null ? content.getContent() : null;
	}

	public DependencyDetails[] compareDependenciesChange(ServiceType serviceType, String dependencyParentFileContent, String dependencyIssueFileContent){
		if(dependencyParentFileContent != null || dependencyIssueFileContent !=null){
			Map<String, String> changedDependencies = new HashMap<>();
			try {
				if (serviceType.equals(ServiceType.MAVEN_JAR) || serviceType.equals(ServiceType.MAVEN_DEPLOYMENT)) {
					changedDependencies = compareDependenciesContentChanges(serviceType, dependencyParentFileContent, dependencyIssueFileContent);
				} else if (serviceType.equals(ServiceType.RELEASE_TAG) || serviceType.equals(ServiceType.UI_CLIENT)) {
					if(dependencyIssueFileContent.contains("\"ng\": \"ng\"") || dependencyIssueFileContent.contains("\"ng\": \"ng\"")){
						changedDependencies = compareDependenciesContentChanges(serviceType, dependencyParentFileContent, dependencyIssueFileContent);
					}
					else {
						changedDependencies = compareDependenciesContentChanges(serviceType, dependencyParentFileContent, dependencyIssueFileContent);
					}
				}
			} catch (Exception e) {
				log.error("Unable to compare pom dependencies ", e);
				throw new BadArgumentsException(ErrorCodes.UNABLE_TO_PARSE_POM_OR_PACKAGE);
			}
			return changedDependencies.entrySet().stream()
					.map(entry -> new DependencyDetails(entry.getKey(), entry.getValue()))
					.toArray(DependencyDetails[]::new);
		}
		return new DependencyDetails[0];
	}

	public Map<String, String> parsePomDependencies(String pomXmlContent) {
		Map<String, String> dependencies = new HashMap<>();

		try {
			DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
			DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
			Document doc = dBuilder.parse(new ByteArrayInputStream(pomXmlContent.getBytes()));

			doc.getDocumentElement().normalize();

			// Parse parent dependencies
			NodeList parentList = doc.getElementsByTagName("parent");
			if (parentList.getLength() > 0) {
				Node parentNode = parentList.item(0);
				if (parentNode.getNodeType() == Node.ELEMENT_NODE) {
					Element parentElement = (Element) parentNode;
					String parentArtifactId = getChildElementValue(parentElement, ARTIFACT_ID);
					String parentVersion = getChildElementValue(parentElement, VERSION);
					String parentGroupId = getChildElementValue(parentElement, "groupId");

					if (parentArtifactId != null && !parentArtifactId.trim().isEmpty()) {
						StringBuilder parentInfo = new StringBuilder();
						if (parentVersion != null && !parentVersion.trim().isEmpty()) {
							parentInfo.append(parentVersion);
						}
						if (parentGroupId != null && !parentGroupId.trim().isEmpty()) {
							parentInfo.append("; GroupId: ").append(parentGroupId);
						}

						dependencies.put("PARENT:" + parentArtifactId, parentInfo.toString());
						log.info("Verifying parent dependency: " + parentArtifactId + " with version: " + parentVersion);
					}
				}
			}

			// Parse regular dependencies
			NodeList dependencyList = doc.getElementsByTagName(DEPENDENCY);

			for (int i = 0; i < dependencyList.getLength(); i++) {
				Node dependencyNode = dependencyList.item(i);

				if (dependencyNode.getNodeType() == Node.ELEMENT_NODE) {
					Element dependencyElement = (Element) dependencyNode;
					String artifactId = getChildElementValue(dependencyElement, ARTIFACT_ID);
					String version = getChildElementValue(dependencyElement, VERSION);

					if (artifactId == null || artifactId.trim().isEmpty()) {
						log.warn("Skipping dependency with missing or empty artifactId");
						continue;
					}
					StringBuilder dependencyInfo = new StringBuilder();
					if (version != null && !version.trim().isEmpty()) {
						dependencyInfo.append(version);
					} else {
						log.warn("Dependency with artifactId '" + artifactId + "' has missing or empty version");
					}

					log.info("Verifying dependency: " + artifactId);

					// Check for exclusions
					NodeList exclusionsList = dependencyElement.getElementsByTagName("exclusions");
					if (exclusionsList.getLength() > 0) {
						Node exclusionsNode = exclusionsList.item(0);
						if (exclusionsNode.getNodeType() == Node.ELEMENT_NODE) {
							Element exclusionsElement = (Element) exclusionsNode;
							NodeList exclusionList = exclusionsElement.getElementsByTagName("exclusion");

							for (int j = 0; j < exclusionList.getLength(); j++) {
								Node exclusionNode = exclusionList.item(j);
								if (exclusionNode.getNodeType() == Node.ELEMENT_NODE) {
									Element exclusionElement = (Element) exclusionNode;
									String excludedGroupId = getChildElementValue(exclusionElement, "groupId");
									String excludedArtifactId = getChildElementValue(exclusionElement, "artifactId");

									if (excludedGroupId != null && excludedArtifactId != null) {
										dependencyInfo.append("; Exclusion: ").append(excludedGroupId).append(":").append(excludedArtifactId);
									} else {
										log.warn("Skipping exclusion with missing groupId or artifactId in dependency: " + artifactId);
									}
								}
							}
						}
					}

					dependencies.put(artifactId, dependencyInfo.toString());
				}
			}
		} catch (Exception e) {
			log.error("Unable to parse pom dependencies", e);
			throw new BadArgumentsException(ErrorCodes.UNABLE_TO_PARSE_POM_OR_PACKAGE);
		}
		return dependencies;
	}

	private String getChildElementValue(Element parentElement, String childTagName) {
		NodeList nodeList = parentElement.getElementsByTagName(childTagName);
		if (nodeList.getLength() > 0) {
			return nodeList.item(0).getTextContent();
		}
		return null;
	}

	public Map<String, String> parsePackageJsonDependencies(String packageJsonFileContent) {
		Map<String, String> dependencies = new HashMap<>();
		try {
			ObjectMapper mapper = new ObjectMapper();
			JsonNode rootNode = mapper.readTree(packageJsonFileContent);

			JsonNode dependenciesNode = rootNode.path("dependencies");
			addPackageJsonDependencies(dependencies, dependenciesNode);

			JsonNode devDependenciesNode = rootNode.path("devDependencies");
			addPackageJsonDependencies(dependencies, devDependenciesNode);
		} catch (IOException e) {
			log.error("Unable to parse package.json dependencies "+e.getMessage());
			throw new BadArgumentsException(ErrorCodes.UNABLE_TO_PARSE_POM_OR_PACKAGE);
		}

		return dependencies;
	}

	private static void addPackageJsonDependencies(Map<String, String> dependenciesMap, JsonNode dependenciesNode) {
		Iterator<Map.Entry<String, JsonNode>> fieldsIterator = dependenciesNode.fields();
		while (fieldsIterator.hasNext()) {
			Map.Entry<String, JsonNode> entry = fieldsIterator.next();
			String key = entry.getKey();
			String value = entry.getValue().asText();
			dependenciesMap.put(key, value);
		}
	}

	public Map<String, String> parseRequirementsDependencies(String requirementsTxtFileContent){
		Map<String, String> dependencies = new HashMap<>();

		try {
			String[] lines = requirementsTxtFileContent.split("\\r?\\n");
			for (String line : lines) {
				String[] parts = line.split("==");
				if (parts.length == 2) {
					dependencies.put(parts[0], parts[1]);
				} else if (parts.length == 1) {
					dependencies.put(parts[0], null);
				}
			}
		} catch (Exception e) {
			log.error("Unable to parse requirement.txt dependencies "+e.getMessage());
			throw new BadArgumentsException(ErrorCodes.UNABLE_TO_PARSE_POM_OR_PACKAGE);
		}
		return dependencies;
	}

	public Map<String, String> compareDependenciesContentChanges(ServiceType serviceType, String parentFileContent, String issueFileContent) {
		Map<String, String> oldDependencies = null;
		Map<String, String> newDependencies = null;
		try {
			if (serviceType.equals(ServiceType.MAVEN_JAR) || serviceType.equals(ServiceType.MAVEN_DEPLOYMENT)) {
				oldDependencies = parsePomDependencies(parentFileContent);
				newDependencies = parsePomDependencies(issueFileContent);
			} else if (serviceType.equals(ServiceType.RELEASE_TAG) || serviceType.equals(ServiceType.UI_CLIENT)) {
				if(parentFileContent.contains("\"ng\": \"ng\"") || issueFileContent.contains("\"ng\": \"ng\"")){
					oldDependencies = parsePackageJsonDependencies(parentFileContent);
					newDependencies = parsePackageJsonDependencies(issueFileContent);
				}
				else {
					oldDependencies = parseRequirementsDependencies(parentFileContent);
					newDependencies = parseRequirementsDependencies(issueFileContent);
				}
			}
		} catch (Exception e) {
			log.error("Unable to compare dependencies", e);
			throw new BadArgumentsException(ErrorCodes.UNABLE_TO_PARSE_POM_OR_PACKAGE);
		}
		Map<String, String> changedDependencies = new HashMap<>();

		// Combine the keys from both maps into a single set to iterate over all unique keys
		Set<String> allKeys = new HashSet<>(newDependencies.keySet());
		allKeys.addAll(oldDependencies.keySet());

		for (String key : allKeys) {
			String newValue = newDependencies.get(key);
			String oldValue = oldDependencies.get(key);

			// Scenario 1: Key exists only in newDependencies (new dependency)
			if (oldValue == null && newValue != null) {
				changedDependencies.put(key, newValue);
			}
			// Scenario 2: Key exists only in oldDependencies (removed dependency)
			else if (oldValue != null && newValue == null) {
				changedDependencies.put(key, null); // Marking as null to show it was removed
			}
			// Scenario 3: Key exists in both maps but the values are different (changed dependency)
			else if (!Objects.equals(newValue, oldValue)) {
				changedDependencies.put(key, newValue);
			}
		}
		return changedDependencies;
	}

	public DependencyDetails[] getParentBranchDependencies(ServiceType serviceType, String parentFileContent){
		Map<String, String> masterDependencies = null;
		if(parentFileContent != null){
			try {
				if (serviceType.equals(ServiceType.MAVEN_JAR) || serviceType.equals(ServiceType.MAVEN_DEPLOYMENT)) {
					masterDependencies = parsePomDependencies(parentFileContent);
				} else if (serviceType.equals(ServiceType.RELEASE_TAG) || serviceType.equals(ServiceType.UI_CLIENT)) {
					if(parentFileContent.contains("\"ng\": \"ng\"")){
						masterDependencies = parsePackageJsonDependencies(parentFileContent);
					}
					else {
						masterDependencies = parseRequirementsDependencies(parentFileContent);
					}
				}
			} catch (Exception e) {
				log.error("Unable to compare dependencies", e);
			}
		}

		return masterDependencies == null ? new DependencyDetails[0] :
				masterDependencies.entrySet().stream()
						.map(entry -> new DependencyDetails(entry.getKey(), entry.getValue()))
						.toArray(DependencyDetails[]::new);
	}

	public JSONObject fetchDependencyFileContentUsingGraphQL(OptimusDeployment optimusDeployment, List<OptimusDeploymentServiceEntity> optimusDeploymentServiceEntityList) throws Exception {
		String graphqlQuery = buildGraphQLQuery(optimusDeployment, optimusDeploymentServiceEntityList);

		String token = getGitHubToken();
		JSONObject response = sendGraphQLRequest(graphqlQuery, token);

		return response;
	}

	private JSONObject sendGraphQLRequest(String graphqlQuery, String token) throws IOException {
		try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
			HttpPost post = new HttpPost(GRAPHQL_URL);
			post.setHeader("Authorization", "Bearer " + token);
			post.setHeader("Content-Type", "application/json");

			JSONObject jsonBody = new JSONObject();
			jsonBody.put("query", graphqlQuery);

			StringEntity entity = new StringEntity(jsonBody.toString());
			post.setEntity(entity);

			org.apache.http.HttpResponse httpResponse = httpClient.execute(post);

			String responseBody = EntityUtils.toString(httpResponse.getEntity());
			return new JSONObject(responseBody);
		}
	}

	private String buildGraphQLQuery(OptimusDeployment optimusDeployment, List<OptimusDeploymentServiceEntity> optimusDeploymentServiceEntityList) {
		StringBuilder queryBuilder = new StringBuilder();
		queryBuilder.append("query {");

		for (OptimusDeploymentServiceEntity serviceEntity : optimusDeploymentServiceEntityList) {
			String repoName = serviceEntity.getService().getRepositoryName().replace("mykaarma/", "");;
			String baseBranch = serviceEntity.getParentBranch();
			String dependencyFilePath = serviceEntity.getService().getDependencyFilePath();
			String serviceName = serviceEntity.getService().getName();
			String sanitizedServiceIdentifier = serviceName.replaceAll("[^A-Za-z0-9_]", "_"); // Prefix and sanitize

			queryBuilder.append(String.format(
					"%s: repository(owner: \"mykaarma\", name: \"%s\") { " +
							"dependencyFileContent: object(expression: \"%s:%s\") { " +
							"... on Blob { text } } } ",
					sanitizedServiceIdentifier, repoName, baseBranch, dependencyFilePath
			));
		}

		queryBuilder.append("}");
        log.info("Built GraphQL query: {}", queryBuilder);
		return queryBuilder.toString();
	}

}