package com.mykaarma.optimus.utils;


import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.mykaarma.optimus.deployment.DeploymentNotificationService;
import com.mykaarma.optimus.model.dto.TestsDTO;
import com.mykaarma.optimus.model.enums.OptimusDeploymentTypeEnum;
import com.mykaarma.optimus.model.panipuri.dto.UserResponseDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mykaarma.optimus.exception.BadArgumentsException;
import com.mykaarma.optimus.jpa.model.ODTestExecutionMapper;
import com.mykaarma.optimus.jpa.model.OptimusDeployment;
import com.mykaarma.optimus.jpa.repository.ODTestExecutionMapperRepository;
import com.mykaarma.optimus.jpa.repository.OptimusDeploymentRepository;
import com.mykaarma.optimus.model.dto.DefaultTestDetailsResponse;
import com.mykaarma.optimus.model.dto.TestDetailsDTO;
import com.mykaarma.optimus.model.enums.AutomationSuiteType;
import com.mykaarma.optimus.model.enums.ErrorCodes;
import com.mykaarma.optimus.model.enums.RolloutStage;
import com.mykaarma.optimus.model.request.AutomationTestRequest;
import com.mykaarma.optimus.model.response.AutomationTestResponse;
import com.mykaarma.optimus.model.response.GetAutomationTestResponse;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class AutomationService {

    private final ODTestExecutionMapperRepository testExecutionMapperRepository;
    private final OptimusDeploymentRepository optimusDeploymentRepository;

    private final NotificationMessageHelper notificationMessageHelper;

    private final DeploymentNotificationService deploymentNotificationService;
    private final PaniPuriService paniPuriService;
    private final RestTemplate restTemplate = new RestTemplate();
    private final String automationBaseUrl;
    private final String automationBaseUrlStable;

    private final String authorization;

    private final String automationSingleDealer= "/automation/singledealer";

    private final String DEPARTMENT = "service";
    private final String ENABLE_PUBNUB_EVENT = "true" ;

    private final String AUTOMATION_SUITE_STATUS_FAILED = "FAILED";


    public AutomationService(ODTestExecutionMapperRepository ODTestExecutionMapperRepository, OptimusDeploymentRepository optimusDeploymentRepository,
                             NotificationMessageHelper notificationMessageHelper, DeploymentNotificationService deploymentNotificationService, PaniPuriService paniPuriService, @Value("${automation.base.url.canary}") final String automationBaseUrl,
                             @Value("${automation.base.url.stable}") final String automationBaseUrlStable,
                             @Value("${automation.authorization}") final String authorization) {
        this.testExecutionMapperRepository = ODTestExecutionMapperRepository;
        this.optimusDeploymentRepository = optimusDeploymentRepository;
        this.notificationMessageHelper = notificationMessageHelper;
        this.deploymentNotificationService = deploymentNotificationService;
        this.paniPuriService = paniPuriService;
        this.automationBaseUrl = automationBaseUrl;
        this.automationBaseUrlStable = automationBaseUrlStable;
        this.authorization = authorization;
    }

    public AutomationTestResponse initiateAutomationSuite(String optimusDeploymentUuid, String userUuid, AutomationTestRequest automationTestRequest) throws IOException {
        List<ODTestExecutionMapper> ODTestExecutionMapperList = testExecutionMapperRepository.findByOptimusDeploymentUuidOrderByInsertTsDesc(optimusDeploymentUuid);
        if(ODTestExecutionMapperList!=null && !ODTestExecutionMapperList.isEmpty()){
            for(ODTestExecutionMapper odTestExecutionMapper : ODTestExecutionMapperList) {
                TestDetailsDTO testDetailsDTOOfLatestSuiteTriggered = getAutomationTestDetailsFromQaAutomation(odTestExecutionMapper.getTestUUID());
                if (testDetailsDTOOfLatestSuiteTriggered != null &&
                        testDetailsDTOOfLatestSuiteTriggered.getTestStatus() != null &&
                        testDetailsDTOOfLatestSuiteTriggered.getTestStatus().equals("IN_PROGRESS") &&
                        testDetailsDTOOfLatestSuiteTriggered.getDealerId() != null &&
                        testDetailsDTOOfLatestSuiteTriggered.getDealerId().equals(automationTestRequest.getDealerId())) {
                    throw new BadArgumentsException(ErrorCodes.AUTOMATION_SUITE_IN_PROGRESS, "One Automation suite is already in progress for this dealerID");
                }
            }
        }
        AutomationTestResponse automationTestResponse = new AutomationTestResponse();
        ODTestExecutionMapper ODTestExecutionMapper = new ODTestExecutionMapper();
        OptimusDeployment optimusDeployment = optimusDeploymentRepository.findByUuid(optimusDeploymentUuid);
        log.info("optimus deployment UUID is " + optimusDeployment.getUuid());
        if(automationTestRequest.getType() == AutomationSuiteType.TEMPLATE){
            // get default data for a test name  /testDetail/defaultData/templates/
            String getDefaultDataUrl = automationBaseUrl + "/testDetail/defaultData/templates/"+ automationTestRequest.getTestName();
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", authorization);
            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<DefaultTestDetailsResponse> response = restTemplate.exchange(getDefaultDataUrl, HttpMethod.GET, entity, DefaultTestDetailsResponse.class);
            log.info("Response from "+ getDefaultDataUrl + " is :{}", response);
            Map<String, String> resultMap = new HashMap<>();
           if(response!=null && Objects.requireNonNull(response.getBody()).getParametersJson()!=null) {
               resultMap = convertJsonToMap(response.getBody().getParametersJson());
           }
           if(automationTestRequest.getEmail()!=null && !automationTestRequest.getEmail().equals(resultMap.get("Email"))){
               resultMap.put("Email", automationTestRequest.getEmail());
           }
            String triggerAutomationSuiteUrl = null;
           if(response.getBody().getRolloutStage().equals("STABLE")){
               triggerAutomationSuiteUrl = automationBaseUrlStable + automationSingleDealer;
            }
           else{
               triggerAutomationSuiteUrl = automationBaseUrl +automationSingleDealer;
           }
           resultMap.put("Enable_Pubnub_Event", ENABLE_PUBNUB_EVENT) ;
           log.info("Url for hitting AutomationSuite is :{}", triggerAutomationSuiteUrl);
            MultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>();
            resultMap.forEach(requestParams::add);
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(requestParams, headers);
            ResponseEntity<AutomationTestResponse> responseEntity  = restTemplate.postForEntity(triggerAutomationSuiteUrl, requestEntity, AutomationTestResponse.class);
                log.info("Response from "+ triggerAutomationSuiteUrl + " is : {}", responseEntity);
                if(responseEntity!=null && responseEntity.getBody() != null&& responseEntity.getStatusCode() == HttpStatusCode.valueOf(200) && responseEntity.getBody().getTestId()!=null){
                    automationTestResponse.setTestId(responseEntity.getBody().getTestId());
                    automationTestResponse.setMessage(responseEntity.getBody().getMessage());
                    ODTestExecutionMapper.setTestName(automationTestRequest.getTestName());
                }
                else{
                    throw new BadArgumentsException(ErrorCodes.UNABLE_TO_RUN_AUTOMATION_SUITE, responseEntity.getBody().getErrors().get(0).getErrorMessage());
                }
        }
        if(automationTestRequest.getType() == AutomationSuiteType.CUSTOMSUITE){
            HttpHeaders headers = new HttpHeaders();
            String triggerAutomationSuiteUrl = null;
            if(automationTestRequest.getRolloutStage().equals(RolloutStage.STABLE)){
                triggerAutomationSuiteUrl = automationBaseUrlStable + automationSingleDealer;
            }
            else{
                triggerAutomationSuiteUrl = automationBaseUrl +automationSingleDealer;
            }
            MultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>();
            requestParams.add("Email", automationTestRequest.getEmail());
            requestParams.add("Test_Type", String.valueOf(automationTestRequest.getType()));
            requestParams.add("Test_List", automationTestRequest.getTestName());
            requestParams.add("Dealer_id", automationTestRequest.getDealerId());
            requestParams.add("Kaarma_Username", automationTestRequest.getKaarmaUsername());
            requestParams.add("Kaarma_Password", automationTestRequest.getKaarmaPassword());
            requestParams.add("Driver_Username", automationTestRequest.getDriverUsername());
            requestParams.add("Driver_Password", automationTestRequest.getDriverPassword());
            requestParams.add("Department_list",DEPARTMENT);
            requestParams.add("Run_Environment", automationTestRequest.getRunEnvironment());
            requestParams.add("Device_Name", automationTestRequest.getDeviceName());
            requestParams.add("Platform_Version", automationTestRequest.getPlatformVersion());
            requestParams.add("Platform", automationTestRequest.getPlatform());
            requestParams.add("Enable_Pubnub_Event", ENABLE_PUBNUB_EVENT);
            headers.set("Authorization", authorization);
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(requestParams, headers);
            ResponseEntity<AutomationTestResponse> responseEntity  = restTemplate.postForEntity(triggerAutomationSuiteUrl, requestEntity, AutomationTestResponse.class);
            log.info("Response from "+ triggerAutomationSuiteUrl + " is : {}", responseEntity);
            if(responseEntity!=null && responseEntity.getBody() != null&& responseEntity.getStatusCode() == HttpStatusCode.valueOf(200) && responseEntity.getBody().getTestId()!=null){
                automationTestResponse.setTestId(responseEntity.getBody().getTestId());
                automationTestResponse.setMessage(responseEntity.getBody().getMessage());
                ODTestExecutionMapper.setTestName(automationTestRequest.getTestName());
            }
            else{
                throw new BadArgumentsException(ErrorCodes.UNABLE_TO_RUN_AUTOMATION_SUITE, responseEntity.getBody().getErrors().get(0).getErrorMessage());
            }
            log.info("Response from "+ triggerAutomationSuiteUrl + " is : {}", responseEntity);
            log.info("TestUUID is " + automationTestResponse.getTestId());
        }
        ODTestExecutionMapper.setTestUUID(automationTestResponse.getTestId());
        ODTestExecutionMapper.setUserUUID(userUuid);
        ODTestExecutionMapper.setOptimusDeployment(optimusDeployment);
        testExecutionMapperRepository.save(ODTestExecutionMapper);
        return automationTestResponse;

    }

    public GetAutomationTestResponse getAutomationTestDetails(String optimusDeploymentUuid){
        GetAutomationTestResponse getAutomationTestResponse = new GetAutomationTestResponse();
        List<TestDetailsDTO> testDetailsList = new ArrayList<>();
        List<ODTestExecutionMapper> ODTestExecutionMapperList = testExecutionMapperRepository.findByOptimusDeploymentUuidOrderByInsertTsDesc(optimusDeploymentUuid);
        for(ODTestExecutionMapper ODTestExecutionMapper : ODTestExecutionMapperList){
        	TestDetailsDTO testDetailsDTO  = getAutomationTestDetailsFromQaAutomation(ODTestExecutionMapper.getTestUUID());
            testDetailsDTO.setTriggeredBy(ODTestExecutionMapper.getUserUUID());
            testDetailsDTO.setInsertTs(ODTestExecutionMapper.getInsertTs());
            testDetailsList.add(testDetailsDTO);
        }
        getAutomationTestResponse.setTestDetails(testDetailsList);
        return getAutomationTestResponse;

    }

    public TestDetailsDTO getAutomationTestDetailsFromQaAutomation(String testUuid) {
        String getDefaultDataUrl = automationBaseUrl+ "/testDetail/" + testUuid ;
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", authorization);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<TestDetailsDTO> response = restTemplate.exchange(getDefaultDataUrl, HttpMethod.GET, entity, TestDetailsDTO.class);
        TestDetailsDTO testDetailsDTO = response.getBody();
        return testDetailsDTO;
		
	}

    public void getODUuidAndSendMessageForAutomationNotification(String testUuid) throws URISyntaxException, IOException {
        ODTestExecutionMapper oDTestExecutionMapper = testExecutionMapperRepository.findByTestUUID(testUuid);
        if(oDTestExecutionMapper!=null &&
                !(oDTestExecutionMapper.getOptimusDeployment().getType().equals(OptimusDeploymentTypeEnum.CANARY2STABLE)
                        && oDTestExecutionMapper.getUserUUID().equals("Optimus NextGen"))){
            String message = "";
            UserResponseDTO userResponseDTO = paniPuriService.getUserByUuid(oDTestExecutionMapper.getUserUUID());
            String automationTriggerUserName = userResponseDTO.getUser().getFirstName() + " " +userResponseDTO.getUser().getLastName();
            TestDetailsDTO testDetailsDTO = getAutomationTestDetailsFromQaAutomation(testUuid);
            if(testDetailsDTO !=null){
                if(testDetailsDTO.getTestStatus().equals(AUTOMATION_SUITE_STATUS_FAILED)){
                    List<String> failedTestNameList = getFailedTestNameList(testDetailsDTO);
                    message = notificationMessageHelper.getMessageForFailedStatusOfAutomationSuite
                            (testUuid, oDTestExecutionMapper.getTestName(), automationTriggerUserName, failedTestNameList);
                }
                else{
                    message = notificationMessageHelper.getMessageForStatusOfAutomationSuite
                            (testDetailsDTO.getTestStatus(), testUuid, oDTestExecutionMapper.getTestName(), automationTriggerUserName);

                }
                deploymentNotificationService.sendOptimusDeploymentNotification(oDTestExecutionMapper.getOptimusDeployment().getUuid(), message);
            }

        }
    }

    private List<String> getFailedTestNameList(TestDetailsDTO testDetailsDTO){
        List<String> failedTestNameList = new ArrayList<>();
        if(testDetailsDTO.getFailedTestList()!=null && !testDetailsDTO.getFailedTestList().isEmpty()){
            for(TestsDTO failedTest: testDetailsDTO.getFailedTestList()){
                failedTestNameList.add(failedTest.getName());
            }
        }
        return failedTestNameList;
    }

	private Map<String, String> convertJsonToMap(String jsonString) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.readValue(jsonString, Map.class);
    }
	
}
