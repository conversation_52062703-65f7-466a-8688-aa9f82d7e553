package com.mykaarma.optimus.jpa.repository;


import com.mykaarma.optimus.jpa.model.DeploymentTarget;
import com.mykaarma.optimus.jpa.model.ServiceEntity;
import com.mykaarma.optimus.model.enums.ServiceType;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface DeploymentTargetRepository extends JpaRepository<DeploymentTarget, Long>{
    DeploymentTarget findByUuid(String uuid);
    @Query("SELECT dt.serviceEntity.name, dt FROM DeploymentTarget dt WHERE dt.serviceEntity.type IN (:serviceType) AND dt.isValid = 1")
    List<Object[]> findServiceNameAndDeploymentTargets(List<ServiceType> serviceType);
    @Query("Select distinct dt.envFilePath FROM DeploymentTarget dt WHERE dt.envFilePath IS NOT NULL AND dt.isValid = 1")
    List<String> findDistinctByEnvFilePathIsNotNull() ;
    @Query("Select dt.serviceEntity FROM DeploymentTarget dt WHERE dt.envVariableName IN (:serviceEnvNameList) AND dt.isValid = 1")
    List<ServiceEntity> findServiceEntityByServiceEnvName(List<String> serviceEnvNameList) ;



}

