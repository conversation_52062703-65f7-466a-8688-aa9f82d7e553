package com.mykaarma.optimus.jpa.model;

import com.fasterxml.jackson.annotation.JsonRawValue;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Entity
@Getter
@Setter
public class  DeploymentTarget {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "templateID")
    private Long templateId;

    @Column(name = "UUID", unique = true)
    private String uuid;

    @Column(name = "Parameter", columnDefinition = "json")
    @JsonRawValue
    private String templateParametersJson;
    private String envFilePath;

    @Column(name = "ServiceEnvName")
    private String envVariableName;

    @ManyToOne
    @JoinColumn(name = "ServiceID")
    private ServiceEntity serviceEntity;

    @Column(name = "isValid", nullable = false)
    private Boolean isValid = true;


    public Long getServiceEntity_id(){
        return serviceEntity.getId();
    }
}
