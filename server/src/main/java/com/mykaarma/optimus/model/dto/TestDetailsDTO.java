package com.mykaarma.optimus.model.dto;

import com.mykaarma.optimus.model.enums.TestStatuses;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class TestDetailsDTO {
    private String testID;
    private String reportPortalLink;
    private Long passedTestCount;
    private String testStatus;
    private String triggeredBy;
    private Long dealerId;
    private Timestamp insertTs;
    private String dealerID;
    List<TestsDTO> failedTestList;
}
