package com.mykaarma.optimus.validation;


import java.io.IOException;
import java.net.URISyntaxException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.amazonaws.util.StringUtils;
import com.mykaarma.optimus.constants.SpecialCharactersConstants;
import com.mykaarma.optimus.constants.StringConstants;
import com.mykaarma.optimus.jpa.model.*;
import com.mykaarma.optimus.jpa.repository.*;
import com.mykaarma.optimus.mongo.repository.OptimusDeploymentSbomRepository;
import com.mykaarma.optimus.utils.JiraService;
import org.codehaus.jackson.JsonGenerationException;
import org.codehaus.jackson.map.JsonMappingException;
import org.json.JSONArray;
import org.json.JSONObject;
import org.kie.api.definition.KiePackage;
import org.kie.api.definition.rule.Rule;
import org.kie.api.runtime.KieSession;
import org.kohsuke.github.*;
import org.springframework.beans.factory.annotation.Value;
import com.mykaarma.optimus.utils.HttpUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.mykaarma.optimus.constants.IntegerConstants;
import com.mykaarma.optimus.deployment.DeploymentNotificationService;
import com.mykaarma.optimus.deployment.OptimusDeploymentActionsService;
import com.mykaarma.optimus.exception.BadArgumentsException;
import com.mykaarma.optimus.model.dto.BabaInfo;
import com.mykaarma.optimus.model.dto.DependencyDetails;
import com.mykaarma.optimus.model.dto.GlobalStateDTO;
import com.mykaarma.optimus.model.dto.OptimusDeploymentActionsDTO;
import com.mykaarma.optimus.model.dto.OptimusDeploymentPrActionsDTO;
import com.mykaarma.optimus.model.dto.OptimusDeploymentServiceActionsDTO;
import com.mykaarma.optimus.model.dto.PomContentDTO;
import com.mykaarma.optimus.model.dto.PrOverrideRulesDTO;
import com.mykaarma.optimus.model.dto.PrStateDTO;
import com.mykaarma.optimus.model.dto.PrValidationResultDTO;
import com.mykaarma.optimus.model.dto.ServiceOverrideRulesDTO;
import com.mykaarma.optimus.model.dto.ServiceStateDTO;
import com.mykaarma.optimus.model.dto.ServiceValidationResultDTO;
import com.mykaarma.optimus.model.dto.ValidationInfoDTO;
import com.mykaarma.optimus.model.dto.ValidationOverrideInfoDTO;
import com.mykaarma.optimus.model.dto.ValidationResultDTO;
import com.mykaarma.optimus.model.dto.VulnerabilityDTO;
import com.mykaarma.optimus.model.enums.CanaryStatus;
import com.mykaarma.optimus.model.enums.ErrorCodes;
import com.mykaarma.optimus.model.enums.OptimusDeploymentAction;
import com.mykaarma.optimus.model.enums.OptimusDeploymentPRStatus;
import com.mykaarma.optimus.model.enums.OptimusDeploymentServiceStatus;
import com.mykaarma.optimus.model.enums.OptimusDeploymentStatus;
import com.mykaarma.optimus.model.enums.OptimusDeploymentTypeEnum;
import com.mykaarma.optimus.model.enums.ServiceType;
import com.mykaarma.optimus.model.enums.ValidationOverrideStatus;
import com.mykaarma.optimus.model.panipuri.dto.RequestInfo;
import com.mykaarma.optimus.model.panipuri.dto.UserResponseDTO;
import com.mykaarma.optimus.model.response.ValidationOverrideResponse;
import com.mykaarma.optimus.mongo.model.OptimusDeploymentDependencyData;
import com.mykaarma.optimus.mongo.model.OverridableRules;
import com.mykaarma.optimus.mongo.model.OverrideRequestInfo;
import com.mykaarma.optimus.mongo.repository.OptimusDeploymentDependencyDataRepository;
import com.mykaarma.optimus.mongo.repository.OverridableRulesRepository;
import com.mykaarma.optimus.mongo.repository.OverrideRequestsRepository;
import com.mykaarma.optimus.utils.ArtifactoryUtils;
import com.mykaarma.optimus.utils.AwsUtils;
import com.mykaarma.optimus.utils.CommonUtils;
import com.mykaarma.optimus.utils.DateFormatter;
import com.mykaarma.optimus.utils.GithubService;
import com.mykaarma.optimus.utils.NotificationMessageHelper;
import com.mykaarma.optimus.utils.PaniPuriService;
import com.mykaarma.optimus.utils.PubNubService;
import com.mykaarma.optimus.utils.RedisService;
import com.mykaarma.optimus.utils.VulnerabilityAnalysisService;
import com.nimbusds.jose.JWSObject;
import com.nimbusds.jwt.JWTClaimsSet;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ValidationService
 {
     private final OptimusDeploymentSbomRepository optimusDeploymentSbomRepository;
     private String paniPuriConsoleUrl;
    private String defectDojoUri;
    private final RulesEngineService rulesEngineService;
    private final GithubService githubService;
    private final OptimusDeploymentServiceRepository optimusDeploymentServiceRepository;
    private final ValidationMongoService validationMongoService;
    private final OptimusDeploymentRepository optimusDeploymentRepository;
    private final BlacklistedServiceInfoRepository blacklistedServiceInfoRepository;
    private final PaniPuriService paniPuriService;
    private final DeploymentNotificationService deploymentNotificationService;
    private final OptimusDeploymentActionsService optimusDeploymentActionsService;
    private final ExecutionRepository executionRepository;
    private final NotificationMessageHelper notificationMessageHelper;
    private final OverridableRulesRepository overridableRulesRepository;
    private final ArtifactoryUtils artifactoryUtils;
    private final AwsUtils awsUtils;
    private final BabaRepository babaRepository;
    private final OverrideRequestsRepository overrideRequestsRepository;
    private final String registryId;
    private final String environment;
    private final String reportPortalUrl;
    private final String reportPortalApiPath;
    private final String addedVulnerabilityRuleName = "Added Vulnerabilities" ;
    private final String reportPortalApiKey;
    private final Double automationSuitePassedPercentage;
    private final static String MYKAARMA_CONFIG = "mykaarma-config";
    private final RedisService redisService;
     private final OptimusDeploymentDependencyDataRepository optimusDeploymentDependencyDataRepository;
     private final VulnerabilityAnalysisService vulnerabilityAnalysisService;
     private final Boolean analysisEnable;
     private final ServiceRepository serviceRepository;
     private final JiraService jiraService;


     public ValidationService(RulesEngineService rulesEngineService, GithubService githubService,
                              OptimusDeploymentServiceRepository optimusDeploymentServiceRepository,
                              ValidationMongoService validationMongoService, OptimusDeploymentRepository optimusDeploymentRepository,
                              PaniPuriService paniPuriService, DeploymentNotificationService deploymentNotificationService, AwsUtils awsUtils, @Value("${aws.ecr.registry.id}") final String registryId,
                              @Value("${spring.profiles.active}") final String environment, BlacklistedServiceInfoRepository blacklistedServiceInfoRepository,
                              OptimusDeploymentActionsService optimusDeploymentActionsService, ArtifactoryUtils artifactoryUtils, ExecutionRepository executionRepository,
                              NotificationMessageHelper notificationMessageHelper, OverridableRulesRepository overridableRulesRepository, BabaRepository babaRepository,
                              OverrideRequestsRepository overrideRequestsRepository, RedisService redisService, OptimusDeploymentDependencyDataRepository optimusDeploymentDependencyDataRepository,
                              VulnerabilityAnalysisService vulnerabilityAnalysisService, @Value("${deployment.analysis.enable}") final Boolean analysisEnable, @Value("${reportportal.base.url}") final String reportPortalUrl,
                              @Value("${reportportal.api.path.url}") final String reportPortalApiPath, @Value("${reportportal.api.key}") final String reportPortalApiKey,
                              @Value("${automationsuite.passed.percentage}") final Double automationSuitePassedPercentage, ServiceRepository serviceRepository, JiraService jiraService,
                              @Value("${panipuri.console-url}") final String paniPuriConsoleUrl ,
                              @Value("${defectdojo.uri}") final String defectDojoUri, OptimusDeploymentSbomRepository optimusDeploymentSbomRepository) {
						this.rulesEngineService = rulesEngineService;
						this.githubService = githubService;
						this.optimusDeploymentServiceRepository = optimusDeploymentServiceRepository;
						this.validationMongoService = validationMongoService;
						this.optimusDeploymentRepository = optimusDeploymentRepository;
                        this.blacklistedServiceInfoRepository = blacklistedServiceInfoRepository;
						this.paniPuriService = paniPuriService;
						this.deploymentNotificationService = deploymentNotificationService;
						this.optimusDeploymentActionsService = optimusDeploymentActionsService;
						this.awsUtils = awsUtils;
						this.environment = environment;
						this.registryId = registryId;
						this.artifactoryUtils = artifactoryUtils;
						this.executionRepository = executionRepository;
                        this.overridableRulesRepository = overridableRulesRepository;
                        this.babaRepository = babaRepository;
                        this.overrideRequestsRepository = overrideRequestsRepository;
                        this.notificationMessageHelper = notificationMessageHelper;
                        this.redisService = redisService;
                        this.vulnerabilityAnalysisService = vulnerabilityAnalysisService;
                        this.optimusDeploymentDependencyDataRepository = optimusDeploymentDependencyDataRepository;
                        this.analysisEnable = analysisEnable;
                        this.reportPortalUrl = reportPortalUrl;
                        this.reportPortalApiPath = reportPortalApiPath;
                        this.reportPortalApiKey = reportPortalApiKey;
                        this.automationSuitePassedPercentage = automationSuitePassedPercentage;
                        this.serviceRepository = serviceRepository;
                        this.paniPuriConsoleUrl = paniPuriConsoleUrl;
                        this.defectDojoUri = defectDojoUri;
                        this.jiraService = jiraService;
         this.optimusDeploymentSbomRepository = optimusDeploymentSbomRepository;
     }

    public ValidationResultDTO validate(String optimusDeploymentUUID,
                                        OptimusDeploymentActionsDTO optimusDeploymentActionsDTO, OptimusDeployment optimusDeployment, String token)
            throws Exception {
        String userTeam = optimusDeployment.getTeam();
        log.info("user team name is " + userTeam);
        log.info("Begining validation of optimus deployment with UUID " + optimusDeploymentUUID
                + " , recieved optimusDeploymentActionsDTO " + optimusDeploymentActionsDTO);
        ExecutorService executionPool = Executors.newFixedThreadPool(10);

        if (optimusDeployment == null) {
            throw new BadArgumentsException(ErrorCodes.INVALID_OPTIMUS_UUID,
                    "Optimus uuid not found : " + optimusDeploymentUUID);
        }
        OptimusDeploymentActionsDTO processedOptimusDeploymentActionsDTO = optimusDeploymentActionsService
                .processOptimusDeploymentActionsDTO(optimusDeploymentActionsDTO);
        log.info("Processed processedOptimusDeploymentActionsDTO " + processedOptimusDeploymentActionsDTO);

        if (processedOptimusDeploymentActionsDTO.getPrs() == null && processedOptimusDeploymentActionsDTO.getServicesWithoutPr()==null) {
            throw new BadArgumentsException(ErrorCodes.NO_PR_OR_SERVICE_IN_DEPLOYMENT,
                    "PR not found for optimusUuid : " + optimusDeploymentUUID);
        }
        ValidationResultDTO validationResult = initializeValidationResultDTO(optimusDeploymentUUID);
        log.info("Starting global validation for this deployment : "+optimusDeploymentUUID);
        GlobalStateDTO globalState = fetchGlobalStateDetails(optimusDeploymentUUID);
        validate(globalState, validationResult, new ArrayList<>());
        if (!validationResult.getGlobalValidationErrors().isEmpty()) {
            validationResult.setIsValid(false);
        } else {
            validationResult.setIsValid(true);
        }

        log.info("Saving parent branch dependencies in mongodb for deploymentUuid: {}", optimusDeploymentUUID);
        saveServicesMasterBranchDependencyDataInMongoDB(optimusDeployment);

        //Check if the optimus Deployment has PRs , if yes then it will go for PR validation
        if(processedOptimusDeploymentActionsDTO.getPrs()!=null && !processedOptimusDeploymentActionsDTO.getPrs().isEmpty()) {
        List<PrValidationResultDTO> prValidationResultList = new ArrayList<>();
        HashMap<String, Double> teamAndAutomationPassedPercentage = getTeamAndAutomationPassedPercentage();
        HashMap<String,Double> paniPuriTeamAndAutomationPassedPercentage = getautomationTeamNameAndPaniPuriTeamNameMap(teamAndAutomationPassedPercentage);
        for (OptimusDeploymentPrActionsDTO pr : processedOptimusDeploymentActionsDTO.getPrs()) {
            executionPool.submit(() -> {

                PrValidationResultDTO prValidationResult = validatePRInParallelThreads(optimusDeploymentUUID, pr,
                        optimusDeployment, paniPuriTeamAndAutomationPassedPercentage, userTeam);
                prValidationResultList.add(prValidationResult);

                if (!prValidationResult.getIsValid()) {
                    validationResult.setIsValid(false);
                }
            });

        }

        try {
            executionPool.shutdown();
            executionPool.awaitTermination(400, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("Error while awaiting termination of execution poll " + e.getStackTrace().toString());
            executionPool.shutdown();
        }
        validationResult.setPrValidationResult(prValidationResultList);
        validationResult.setIsOverridable(isRulesApplicableForValidationOverride(validationResult.getPrValidationResult()));
	
        
        } // If the deployment has services without PR then it will go for service validation only
        else if (processedOptimusDeploymentActionsDTO.getServicesWithoutPr() != null
			&& !processedOptimusDeploymentActionsDTO.getServicesWithoutPr().isEmpty()) {
		List<ServiceValidationResultDTO> serviceWithoutPRValidationResultList = new ArrayList<>();

		for (OptimusDeploymentServiceActionsDTO service : processedOptimusDeploymentActionsDTO.getServicesWithoutPr()) {
			executionPool.submit(() -> {
                ServiceValidationResultDTO serviceValidationResult = null;
                try {
                    serviceValidationResult = validateServiceInParallelThreads(service,
                            optimusDeployment);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                if (!serviceValidationResult.getIsValid()) {
					validationResult.setIsValid(false);
				}
				serviceWithoutPRValidationResultList.add(serviceValidationResult);
			});

		}

		try {
			executionPool.shutdown();
			executionPool.awaitTermination(400, TimeUnit.SECONDS);
		} catch (InterruptedException e) {
			log.error("Error while awaiting termination of execution poll " + e.getStackTrace().toString());
			executionPool.shutdown();
		}

		validationResult.setServiceWithoutPrValidationResult(serviceWithoutPRValidationResultList);
		validationResult.setIsOverridable(isRulesApplicableForValidationOverrideForServiceWithoutPR(
				validationResult.getServiceWithoutPrValidationResult()));

	}

        log.info("Saving Validation Result in Mongo: " + validationResult);
        validationMongoService.saveValidationResults(validationResult);
        //conflicting deployment check.
        log.info("Checking if any conflicting deployment for deploymentUuid: {}", optimusDeploymentUUID);
        subscribeUserToConflictingDeployment(validationResult, token);

        return validationResult;
    }

    private HashMap<String, Double> getTeamAndAutomationPassedPercentage(){
        HashMap<String, Double> teamAndAutomationPassedPercentage = new HashMap<>();
        try {
            String responseBody = HttpUtils.getRequestWithApiKeyAuthAndBearer(reportPortalUrl + reportPortalApiPath, reportPortalApiKey);
            JSONObject jsonObject = new JSONObject(responseBody);
            JSONObject contentObject = jsonObject.getJSONObject("content");
            JSONArray result = contentObject.getJSONArray("result");
            for (int i = 0; i < result.length(); i++) {
                JSONObject resultObject = result.getJSONObject(i);
                String team = resultObject.getString("attributeValue");
                double passingRate = resultObject.getDouble("passingRate");
                teamAndAutomationPassedPercentage.put(team, passingRate);
            }
            for (Map.Entry<String, Double> entry : teamAndAutomationPassedPercentage.entrySet()) {
                log.info("Team: " + entry.getKey() + ", Passed Percentage: " + entry.getValue());
            }
        } catch (Exception e) {
            log.error("Error while fetching team and automation passed percentage. Error: {} " , e.getMessage());
        }
        return teamAndAutomationPassedPercentage;
    }


//to-do will remove this methond in coming sprints
    private HashMap<String,Double> getautomationTeamNameAndPaniPuriTeamNameMap(HashMap<String,Double> teamAndAutomationPassedPercentage){
        HashMap<String,Double> paniPuriTeamAndAutomationPassedPercentage = new HashMap<>();
        for (Map.Entry<String, Double> entry : teamAndAutomationPassedPercentage.entrySet()) {
            if(entry.getKey().equals("DMS")){
                paniPuriTeamAndAutomationPassedPercentage.put("Integration", entry.getValue());
            }
            else if(entry.getKey().equals("AAA")){
                paniPuriTeamAndAutomationPassedPercentage.put("AAA - (Automation, Architecture, Admin)", entry.getValue());
            }
            else if(entry.getKey().equals("Scheduler")){
                paniPuriTeamAndAutomationPassedPercentage.put("Scheduler", entry.getValue());
            }
            else if(entry.getKey().equals("Inspection")){
                paniPuriTeamAndAutomationPassedPercentage.put("Inspection", entry.getValue());
            }
            else if(entry.getKey().equals("Messaging")){
                paniPuriTeamAndAutomationPassedPercentage.put("Communication", entry.getValue());
            }
            else if(entry.getKey().equals("Transportation")){
                paniPuriTeamAndAutomationPassedPercentage.put("ATI", entry.getValue());
            }
            else if(entry.getKey().equals("Payment")){
                paniPuriTeamAndAutomationPassedPercentage.put("Payment", entry.getValue());
            }
            else if(entry.getKey().equals("Reporting")){
                paniPuriTeamAndAutomationPassedPercentage.put("Reporting", entry.getValue());
            }
        }


        return paniPuriTeamAndAutomationPassedPercentage;
    }

    private void subscribeUserToConflictingDeployment(ValidationResultDTO validationResult, String token) {
        String userUuid = paniPuriService.getUserInfo(token).getUser().getUuid();
        log.info("Checking conflicting deployment for User uuid: {}", userUuid);
        if(validationResult.getPrValidationResult()!=null && !validationResult.getPrValidationResult().isEmpty()) {
        for (PrValidationResultDTO prValidationResult : validationResult.getPrValidationResult()) {
            if(prValidationResult.getValidationErrors()!= null){
                for (ValidationInfoDTO errorMessage : prValidationResult.getValidationErrors()) {
                    if (errorMessage.getRuleDescription().contains("PR is in Active Deployment ") || errorMessage.getRuleDescription().contains("Merging is not allowed. PR is in Active Canary2Stable Deployment ")) {
                        String errorMsg = errorMessage.getRuleDescription().replaceAll("(.*)MYK(.*)", "MYK$2");
                        String conflictingDeploymentTicket = errorMsg.replaceAll("MYK(.*) (.*)", "MYK$1");
                        OptimusDeployment conflictingOptimusDeployment = optimusDeploymentRepository.findByTicketAndIsValidTrue(conflictingDeploymentTicket);
                        String conflictingOptimusDeploymentUuid = conflictingOptimusDeployment.getUuid();
                        log.info("conflicting OptimusDeploymentUuid is: {}", conflictingOptimusDeployment);
                        String message = notificationMessageHelper.getMessageForConflictingDeployment();
                        deploymentNotificationService.subscribeUserToOptimusDeployment(conflictingOptimusDeploymentUuid, userUuid, token, message);
                    }
                }
            }
        }
        }

    }

    private PrValidationResultDTO validatePRInParallelThreads(String optimusDeploymentUUID,
                                                              OptimusDeploymentPrActionsDTO pr, OptimusDeployment optimusDeployment, HashMap<String, Double> paniPuriTeamAndAutomationPassedPercentage, String userTeam) {

        log.info("Validating PRs in parallel " + pr.getOptimusDeploymentPrUuid());

        PrValidationResultDTO prValidationResult = new PrValidationResultDTO();
        List<ServiceValidationResultDTO> serviceValidationResultList = new ArrayList<>();
        log.info("Validating PR with UUID " + pr.getOptimusDeploymentPrUuid());
        try {
            validatePr(optimusDeploymentUUID, pr, prValidationResult, optimusDeployment, paniPuriTeamAndAutomationPassedPercentage, userTeam);
        } catch (Exception e) {
            log.error(
                    "Encountered error while validation of prUUID " + pr.getOptimusDeploymentPrUuid() + e.getMessage());
        }
        ExecutorService serviceExecutionPool = Executors.newFixedThreadPool(5);

        for (OptimusDeploymentServiceActionsDTO service : pr.getServices()) {
            serviceExecutionPool.submit(() -> {
                ServiceValidationResultDTO serviceValidationResult = null;
                try {
                    serviceValidationResult = validateServiceInParallelThreads(service,
                            optimusDeployment);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                if (!serviceValidationResult.getIsValid()) {
                    prValidationResult.setIsValid(false);
                }
                serviceValidationResultList.add(serviceValidationResult);
            });

        }
        try {
            serviceExecutionPool.shutdown();
            serviceExecutionPool.awaitTermination(400, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("Error while awaiting termination of execution poll " + e.getStackTrace().toString());
            serviceExecutionPool.shutdown();
        }

        log.info("Saving service validation result list " + serviceValidationResultList + " in pr validation result");
        prValidationResult.setServiceValidationResult(serviceValidationResultList);
        return prValidationResult;

    }

    private ServiceValidationResultDTO validateServiceInParallelThreads(OptimusDeploymentServiceActionsDTO service,
                                                                        OptimusDeployment optimusDeployment) throws Exception {

        log.info("Validating services in parallel " + service.getOptimusDeploymentServiceUuid());
        OptimusDeploymentServiceEntity optimusDeploymentServiceEntity = fetchOptimusDeploymentServiceFromOptimusDeployment(
                optimusDeployment, service.getOptimusDeploymentServiceUuid());
        ServiceValidationResultDTO serviceValidationResult = new ServiceValidationResultDTO();

        if (isServiceRuleApplicableForService(optimusDeploymentServiceEntity)) {
            try {
                serviceValidationResult = validateService(service, optimusDeploymentServiceEntity, optimusDeployment.getUuid(), optimusDeployment.getType().getName());
            } catch (Exception e) {
                log.error("Exception encountered while validating service " + service.getOptimusDeploymentServiceUuid()
                        + e);
            }

        }

        return serviceValidationResult;
    }

     private void saveServicesMasterBranchDependencyDataInMongoDB(OptimusDeployment optimusDeployment) throws Exception {

         OptimusDeploymentTypeEnum odType = optimusDeployment.getType().getName();
         DependencyDetails[] dependenciesDetails = null;
         OptimusDeploymentServiceEntity optimusDeploymentServiceEntity = null;
         OptimusDeploymentDependencyData optimusDeploymentDependencyData = null;

         if (analysisEnable && !(odType.equals(OptimusDeploymentTypeEnum.CANARY2STABLE) || odType.equals(OptimusDeploymentTypeEnum.VULNERABILITY_ANALYSIS) ||
                 odType.equals(OptimusDeploymentTypeEnum.MASTER_MIGRATION_QA) || odType.equals(OptimusDeploymentTypeEnum.VULNERABILITY_ANALYSIS_SNYK))) {

             List<OptimusDeploymentServiceEntity> optimusDeploymentServiceEntityList = optimusDeploymentServiceRepository.findByOptimusDeploymentUuid(optimusDeployment.getUuid());

             JSONObject graphQLQueryResponse = githubService.fetchDependencyFileContentUsingGraphQL(optimusDeployment, optimusDeploymentServiceEntityList);

             for (OptimusDeploymentServiceEntity odServiceEntity : optimusDeploymentServiceEntityList) {
                 OptimusDeploymentDependencyData fetchedOptimusDeploymentDependencyData = optimusDeploymentDependencyDataRepository.findByOptimusDeploymentServiceUuidAndIsValidTrue(odServiceEntity.getUuid());
                 if (fetchedOptimusDeploymentDependencyData == null) {
                     String serviceName = odServiceEntity.getService().getName();
                     String sanitizedServiceIdentifier = serviceName.replaceAll("[^A-Za-z0-9_]", "_");
                     ServiceType serviceType = odServiceEntity.getService().getType();
                     String dependencyFileContent = extractDependencyFileContent(sanitizedServiceIdentifier, graphQLQueryResponse);

                     if (dependencyFileContent != null) {
                         dependenciesDetails = githubService.getParentBranchDependencies(serviceType, dependencyFileContent);
                     } else {
                         dependenciesDetails = new DependencyDetails[0];
                         log.info("No dependency file content found for service: {} {}", odServiceEntity.getUuid(), odServiceEntity.getService().getName());
                     }
                     optimusDeploymentDependencyData = new OptimusDeploymentDependencyData();
                     optimusDeploymentDependencyData.setOptimusDeploymentUuid(optimusDeployment.getUuid());
                     optimusDeploymentDependencyData.setOptimusDeploymentServiceUuid(odServiceEntity.getUuid());
                     optimusDeploymentDependencyData.setDependencyDetails(dependenciesDetails);
                     optimusDeploymentDependencyData.setIsDependenciesUpdated(false);
                     optimusDeploymentDependencyData.setIsValid(true);
                     log.info("saving initial data: {}", optimusDeploymentDependencyData);
                     saveDependenciesChangesInMongo(optimusDeploymentDependencyData);
                 }
             }
         }
     }



     private String extractDependencyFileContent(String serviceIdentifier, JSONObject graphQLQueryResponse) {
         try {
             if (graphQLQueryResponse.has("data")) {
                 JSONObject data = graphQLQueryResponse.getJSONObject("data");
                 if (data.has(serviceIdentifier)) {
                     JSONObject serviceData = data.getJSONObject(serviceIdentifier);
                     if (serviceData.has("dependencyFileContent") && !serviceData.isNull("dependencyFileContent")) {
                         JSONObject dependencyFileContentObject = serviceData.getJSONObject("dependencyFileContent");
                         if (dependencyFileContentObject.has("text")) {
                             String dependencyText = dependencyFileContentObject.getString("text");
                             log.info("extracted dependency {}", dependencyText);
                             return dependencyText;
                         }
                     }
                 }
             }
         } catch (Exception e) {
             log.error("Error extracting dependency file content for service UUID: {}", serviceIdentifier, e);
         }
         return null;
     }


    private void
    validatePr(String optimusDeploymentUUID, OptimusDeploymentPrActionsDTO pr,
                            PrValidationResultDTO prValidationResult, OptimusDeployment optimusDeployment, HashMap<String, Double> paniPuriTeamAndAutomationPassedPercentage, String userTeam) throws Exception {
        OptimusDeploymentPR prDetails = fetchPrDetailsFromPrUuid(optimusDeployment, pr.getOptimusDeploymentPrUuid());
        PrStateDTO prState = fetchPrStateDetails(optimusDeploymentUUID, prDetails);
        prState.setAutomationSuitePassRateIsMoreThanLimit(true);

        if(prState.getRepositoryName().equals(StringConstants.API_GATEWAY_REPO.getValue())){
            log.info("PR repository");
            Set<OptimusDeploymentServiceEntity> optimusDeploymentServiceEntities = optimusDeploymentServiceRepository.findByPrUuid(pr.getOptimusDeploymentPrUuid());
            List<OptimusDeploymentServiceEntity> isApiGatewayGlobal =  optimusDeploymentServiceEntities.stream().filter(service -> service.getService().getName().equals(StringConstants.API_GATEWAY_GLOBAL.getValue())).collect(Collectors.toList());
            if (isApiGatewayGlobal.size() != 0) {
                prState.setIsApiGatewayGlobalServiceAdded(true);
            }

            List<OptimusDeploymentServiceEntity> apiGatewayServices = prDetails.getServices().stream().filter(service -> service.getService().getRepositoryName().equals(StringConstants.API_GATEWAY_REPO.getValue())).collect(Collectors.toList());
            if(apiGatewayServices.size() != 0){
                List<ServiceEntity> serviceEntities = serviceRepository.findByRepositoryNameAndIsValidTrue(prDetails.getRepositoryName());
                boolean allGatewayNamespacesPresent = serviceEntities.stream()
                        .map(ServiceEntity::getName)
                        .allMatch(name -> apiGatewayServices.stream()
                                .map(service -> service.getService().getName())
                                .collect(Collectors.toSet())
                                .contains(name));
                if(!allGatewayNamespacesPresent){
                    prState.setIsApiGatewayMergeAllowed(false);
                }
            }
            }
        if (pr.getActions() != null && !pr.getActions().isEmpty()) {
            prState.setActions(pr.getActions());
        }
        else {
        	//Since PR actions are null, the only other options are Build and Deploy
        	prState.setActions(new ArrayList<>(
				Arrays.asList(OptimusDeploymentAction.BUILD, OptimusDeploymentAction.DEPLOY)));
        }
        prState
        .setDeploymentType(prDetails.getOptimusDeployment().getType().getName());
        Set<Execution> executions = executionRepository
                .findByOptimusDeploymentId(prDetails.getOptimusDeployment().getId());
        if (executions != null && executions.size() > 0) {
        	prState.setIsRetried(true);
        }
        if(paniPuriTeamAndAutomationPassedPercentage!=null && !paniPuriTeamAndAutomationPassedPercentage.isEmpty() && paniPuriTeamAndAutomationPassedPercentage.get(userTeam)!=null &&  paniPuriTeamAndAutomationPassedPercentage.get(userTeam)
                < automationSuitePassedPercentage){
            prState.setAutomationSuitePassRateIsMoreThanLimit(false);
        }
        prState.setStatus(prDetails.getStatus());
        prState.setEnvironment(environment);
        List<ValidationInfoDTO> prValidationErrors = new ArrayList<>();
        log.info("PR state for PRUUID: " + pr.getOptimusDeploymentPrUuid() + " is " + prState);
        prValidationResult.setOptimusDeploymentPrUuid(pr.getOptimusDeploymentPrUuid());
        prValidationResult.setValidationErrors(prValidationErrors);
        log.info("Validating PR for PRUUID: " + pr.getOptimusDeploymentPrUuid());
        List<String> rulesList = getRulesForValidationOverride(optimusDeploymentUUID+":"+pr.getOptimusDeploymentPrUuid());
        log.info("Rules list is : "+rulesList);
        validate(prState, prValidationResult, rulesList);
        if (!prValidationErrors.isEmpty()) {
            prValidationResult.setIsValid(false);
        } else {
            prValidationResult.setIsValid(true);
        }


    }

    private OptimusDeploymentPR fetchPrDetailsFromPrUuid(OptimusDeployment optimusDeployment, String prUuid) {
        OptimusDeploymentPR optimusDeploymentPR = new OptimusDeploymentPR();
        for (OptimusDeploymentPR pr : optimusDeployment.getPrs()) {
            if (pr.getUuid().equals(prUuid)) {
                optimusDeploymentPR = pr;
            }
        }
        return optimusDeploymentPR;
    }

     private ServiceValidationResultDTO validateService(OptimusDeploymentServiceActionsDTO service,
                                                        OptimusDeploymentServiceEntity optimusDeploymentServiceEntity, String optimusDeploymentUuid, OptimusDeploymentTypeEnum odType)
             throws IOException, URISyntaxException, InterruptedException {
         log.info("Validating service with UUID " + service.getOptimusDeploymentServiceUuid());
         ServiceValidationResultDTO serviceValidationResult = new ServiceValidationResultDTO();
         List<ValidationInfoDTO> serviceValidationErrors = new ArrayList<>();
         serviceValidationResult.setValidationErrors(serviceValidationErrors);
         ServiceStateDTO serviceState = fetchServiceStateDetails(service.getActions(), optimusDeploymentServiceEntity);
         log.info("Service state for optimusDeploymentServiceUuid " + service.getOptimusDeploymentServiceUuid() + " : "
                 + serviceState);
         OptimusDeployment optimusDeployment = optimusDeploymentRepository.findByUuid(optimusDeploymentUuid);

         boolean noAnalysisRequired = false;
         if(service.getActions() != null){
              if(service.getActions().contains(OptimusDeploymentAction.MERGE) ||
                     service.getActions().contains(OptimusDeploymentAction.MARK_AS_VERIFIED) ||
                     service.getActions().contains(OptimusDeploymentAction.REVERT)){
                  noAnalysisRequired = true;
                  log.info("Service action contains analysis status "+noAnalysisRequired);
              }
         }


         if(!noAnalysisRequired && analysisEnable && !(odType.equals(OptimusDeploymentTypeEnum.CANARY2STABLE) || odType.equals(OptimusDeploymentTypeEnum.VULNERABILITY_ANALYSIS) ||
                 odType.equals(OptimusDeploymentTypeEnum.MASTER_MIGRATION_QA) || odType.equals(OptimusDeploymentTypeEnum.VULNERABILITY_ANALYSIS_SNYK))){
             if(!(optimusDeploymentServiceEntity.getStatus().equals(OptimusDeploymentServiceStatus.ANALYSIS_IN_PROGRESS) ||
                     optimusDeploymentServiceEntity.getStatus().equals(OptimusDeploymentServiceStatus.VA_SERVICE_PUSHED_DT) ||
                     optimusDeploymentServiceEntity.getStatus().equals(OptimusDeploymentServiceStatus.VA_SERVICE_SAVED_DT_FINDINGS) ||
                     optimusDeploymentServiceEntity.getStatus().equals(OptimusDeploymentServiceStatus.VA_SERVICE_PUSHED_DEFECTDOJO))) {
                     OptimusDeploymentDependencyData optimusDeploymentDependencyData = optimusDeploymentDependencyDataRepository.findByOptimusDeploymentServiceUuidAndIsValidTrue(optimusDeploymentServiceEntity.getUuid());
                     log.info("optimusDeploymentDependencyData: {}", optimusDeploymentDependencyData);
                     String dependencyIssueFileContent = githubService.getDependenciesFilePath(optimusDeploymentServiceEntity.getService().getType(), optimusDeploymentServiceEntity.getIssueBranch(), optimusDeploymentServiceEntity.getService().getRepositoryName(), optimusDeploymentServiceEntity.getService().getFolderPath());
                     DependencyDetails[] issueBranchDependencies = getDependenciesInSubsequentReDeployments(optimusDeploymentServiceEntity.getService().getType(), dependencyIssueFileContent, optimusDeploymentDependencyData);
                     boolean isDependencyUpdated = isDependencyUpdatedInCurrentDeployment(optimusDeploymentServiceEntity.getService().getType(), dependencyIssueFileContent, optimusDeploymentDependencyData);
                     log.info("isDependencyUpdated: {}", isDependencyUpdated);
                     optimusDeploymentDependencyData.setIsDependenciesUpdated(isDependencyUpdated);
                     optimusDeploymentDependencyData.setDependencyDetails(issueBranchDependencies);
                     saveDependenciesChangesInMongo(optimusDeploymentDependencyData);
                 // Run Analysis
                     serviceState.setIsDependencyUpdated(optimusDeploymentDependencyData.getIsDependenciesUpdated());
                     if(optimusDeploymentDependencyData.getIsDependenciesUpdated()){
                         log.info("Running Analysis for service "+optimusDeploymentServiceEntity.getService().getName());
                         runAnalysis(optimusDeploymentServiceEntity.getUuid(), optimusDeployment);
                         optimusDeploymentServiceEntity.setStatus(OptimusDeploymentServiceStatus.ANALYSIS_IN_PROGRESS);
                         optimusDeploymentServiceRepository.save(optimusDeploymentServiceEntity);
                     }
                 }
         }

         VulnerabilityDTO vulnerabilityDTO = vulnerabilityAnalysisService.getVulnerabilityForService(optimusDeploymentUuid, optimusDeploymentServiceEntity.getService().getUuid());
         if(vulnerabilityDTO != null){
             // in future we can apply rule for all vulnerabilities
//             long totalAdded = vulnerabilityDTO.getAddedCritical() + vulnerabilityDTO.getAddedHigh() + vulnerabilityDTO.getAddedLow() + vulnerabilityDTO.getAddedMedium();
             if(vulnerabilityDTO.getAddedCritical() > 0){
                 serviceState.setIsCriticalVulnerabilitiesAdded(true);
                 List<String> vaMessageList = new ArrayList<>();
                 if(vulnerabilityDTO.getAddedCritical() > 0){
                     vaMessageList.add("Critical : "+vulnerabilityDTO.getAddedCritical());
                 }
                 if(vulnerabilityDTO.getAddedHigh() > 0){
                     vaMessageList.add("High : "+vulnerabilityDTO.getAddedHigh());
                 }
                 if(vulnerabilityDTO.getAddedMedium() > 0){
                     vaMessageList.add("Medium : "+vulnerabilityDTO.getAddedMedium());
                 }
                 if(vulnerabilityDTO.getAddedLow() > 0){
                     vaMessageList.add("Low : "+vulnerabilityDTO.getAddedLow());
                 }
                 serviceState.setAddedVulnerabilities(String.join(", ", vaMessageList));
             }
         }
         List<String> rulesList = getRulesForValidationOverride(optimusDeploymentUuid+":"+service.getOptimusDeploymentServiceUuid());
         log.info("Service Rules list is : "+rulesList);
         validate(serviceState, serviceValidationResult, rulesList);
         serviceValidationResult.setOptimusDeploymentServiceUuid(service.getOptimusDeploymentServiceUuid());
         serviceValidationResult.setValidationErrors(serviceValidationErrors);
         if (!serviceValidationErrors.isEmpty()) {
             serviceValidationResult.setIsValid(false);
         } else {
             serviceValidationResult.setIsValid(true);
         }

         log.info("Service Validation Result for optimusDeploymentServiceUuid "
                 + service.getOptimusDeploymentServiceUuid() + " : " + serviceValidationResult);
         return serviceValidationResult;

     }

	public GlobalStateDTO fetchGlobalStateDetails(String optimusDeploymentUUID) {
        GlobalStateDTO globalState = new GlobalStateDTO();
        log.info("Global State for optimus deployment UUID " + optimusDeploymentUUID + " is " + globalState);
        return globalState;
    }

    public boolean isServiceRuleApplicableForService(OptimusDeploymentServiceEntity optimusDeploymentServiceEntity) {
        if (optimusDeploymentServiceEntity != null
                && !optimusDeploymentServiceEntity.getService().getName().equalsIgnoreCase(MYKAARMA_CONFIG)) {

            log.info("ServiceRule is Applicable For Service " + optimusDeploymentServiceEntity.getService().getName());
            return true;
        }
        log.info("ServiceRule is Applicable For Service " + MYKAARMA_CONFIG);

        return false;
    }

    public ServiceStateDTO fetchServiceStateDetails(List<OptimusDeploymentAction> actions,
                                                    OptimusDeploymentServiceEntity optimusDeploymentServiceEntity)
            throws JsonGenerationException, JsonMappingException, IOException, URISyntaxException, InterruptedException {

        ServiceStateDTO serviceStateDTO = new ServiceStateDTO();

        List<OptimusDeploymentPR> filteredPRs = optimusDeploymentServiceEntity.getOptimusDeployment()
                .getPrs()
                .stream()
                .filter(pr -> StringConstants.API_GATEWAY_REPO.getValue().equals(pr.getRepositoryName())) // Filter out those with repositoryName "api-gateway"
                .collect(Collectors.toList());

        if(filteredPRs.size() != 0 && optimusDeploymentServiceEntity.getService().getName().contains(StringConstants.API_GATEWAY.getValue()+"-")) {

            Set<OptimusDeploymentServiceEntity> optimusDeploymentServiceEntities = optimusDeploymentServiceRepository.findByPrUuid(filteredPRs.get(0).getUuid());
            List<OptimusDeploymentServiceEntity> isApiGatewayGlobal =  optimusDeploymentServiceEntities.stream().filter(service -> service.getService().getName().equals(StringConstants.API_GATEWAY_GLOBAL.getValue())).collect(Collectors.toList());

            if (isApiGatewayGlobal.size() !=0 ) {
                log.info("Validation api-gateway-global service is present");
                if(!isApiGatewayGlobal.get(0).getIssueBranchVersion().equals(optimusDeploymentServiceEntity.getIssueBranchVersion())){
                    serviceStateDTO.setIsApiGatewayNamespacesVersionMatched(false);
                    serviceStateDTO.setIsApiGatewayNamespacesPresent(true);
                }
                else {
                    serviceStateDTO.setIsApiGatewayNamespacesPresent(true);
                }
            }
        }

        serviceStateDTO
                .setDeploymentType(optimusDeploymentServiceEntity.getOptimusDeployment().getType().getName());
        serviceStateDTO.setServiceName(optimusDeploymentServiceEntity.getService().getName());
        serviceStateDTO.setEnvironment(environment);
            Set<Execution> executions = executionRepository
                .findByOptimusDeploymentId(optimusDeploymentServiceEntity.getOptimusDeployment().getId());
        if (executions != null && executions.size() > 0) {
            serviceStateDTO.setIsRetried(true);
        }
        serviceStateDTO.setActions(actions);
        serviceStateDTO.setIssueBranchVersion(optimusDeploymentServiceEntity.getIssueBranchVersion());
        serviceStateDTO.setParentBranchVersion(optimusDeploymentServiceEntity.getParentBranchVersion());
        serviceStateDTO.setServiceDeploymentState(optimusDeploymentServiceEntity.getStatus());
        serviceStateDTO.setCanaryStatus(optimusDeploymentServiceEntity.getService().getCanaryStatus());
        if(optimusDeploymentServiceEntity.getOptimusDeployment().getType().getName().equals(OptimusDeploymentTypeEnum.CANARY2STABLE))
        {
        	OptimusDeployment latestOD = optimusDeploymentRepository.findLatestCanary2StableOptimusDeploymentForService(optimusDeploymentServiceEntity.getService().getUuid());
        	if(!latestOD.getUuid().equals(optimusDeploymentServiceEntity.getOptimusDeployment().getUuid()))
        	{
        		log.warn("User trying to take action on old CanaryToStable Deployment");;
        		serviceStateDTO.setIsLatestCanary2StableDeployment(false);
        		serviceStateDTO.setLatestCanary2StableDeploymentTicket(latestOD.getTicket());
        	}
        }
        
		if (optimusDeploymentServiceEntity.getService().getDeploymentTargets() != null
				&& !optimusDeploymentServiceEntity.getService().getDeploymentTargets().isEmpty()) {
			log.info("Service has no deployment targets " + optimusDeploymentServiceEntity.getUuid());
			for (DeploymentTarget deploymentTargets : optimusDeploymentServiceEntity.getService()
					.getDeploymentTargets()) {
				if (deploymentTargets.getIsValid()) {
					log.info("Service {} has valid deployment targets " , optimusDeploymentServiceEntity.getService().getName());
					serviceStateDTO.setIsDeployable(true);
				}
			}
		}

        if (optimusDeploymentServiceEntity.getService().getType().equals(ServiceType.RELEASE_TAG) || optimusDeploymentServiceEntity.getService().getType().equals(ServiceType.HAPROXY) || optimusDeploymentServiceEntity.getService().getType().equals(ServiceType.NPM_PACKAGE) || optimusDeploymentServiceEntity.getService().getType().equals(ServiceType.UI_CLIENT)) {
            if (optimusDeploymentServiceEntity.getIssueBranchVersion() != null
                    && !optimusDeploymentServiceEntity.getIssueBranchVersion().isEmpty()) {
                serviceStateDTO.setIsReleaseTagCreated(githubService.isReleaseTagCreated(
                        optimusDeploymentServiceEntity.getService().getRepositoryName(),
                        optimusDeploymentServiceEntity.getService().getReleaseTagFormat().replaceAll("\\$\\{version\\}",
                                optimusDeploymentServiceEntity.getIssueBranchVersion())));
            }
        }

        if (optimusDeploymentServiceEntity.getService().getDockerImage() != null
                && !optimusDeploymentServiceEntity.getService().getDockerImage().isEmpty()) {
                serviceStateDTO.setIssueBranchVersionExistsInEcr(awsUtils.isImageExistsInECR(registryId,
                        optimusDeploymentServiceEntity.getService().getDockerImage(),
                        serviceStateDTO.getIssueBranchVersion()));
        } else {
            if (optimusDeploymentServiceEntity.getService().getType().equals(ServiceType.MAVEN_JAR)) {

                String versionFilePath = githubService.getVersionFilePath(optimusDeploymentServiceEntity.getService());
                GHRepository githubRepository = githubService.getRepository(optimusDeploymentServiceEntity.getService().getRepositoryName());
                GHContent pomContent = githubService.getFileFromGithub(githubRepository,
                        optimusDeploymentServiceEntity.getIssueBranch(), versionFilePath);
                PomContentDTO pomContentDTO = githubService.readPomDetailsFromGithub(pomContent);
                serviceStateDTO.setIsBuildExistInArtifactory(artifactoryUtils.checkVersionInArtifactory(
                        pomContentDTO.getGroupId(), pomContentDTO.getArtifactId(), pomContentDTO.getVersion()));
            } else {
                serviceStateDTO.setIsBuildExistInArtifactory(false);
            }
            serviceStateDTO.setIssueBranchVersionExistsInEcr(false);
        }
        serviceStateDTO.setServiceType(optimusDeploymentServiceEntity.getService().getType());
       
        serviceStateDTO.setIsIssueBranchVersionBlacklisted(isVersionBlacklisted(optimusDeploymentServiceEntity.getService().getUuid(),
    	            optimusDeploymentServiceEntity.getIssueBranchVersion()));
        serviceStateDTO.setIsParentBranchVersionBlacklisted(isVersionBlacklisted(optimusDeploymentServiceEntity.getService().getUuid(),
	            optimusDeploymentServiceEntity.getParentBranchVersion()));
        
        log.info("Service State DTO : " + serviceStateDTO);
        return serviceStateDTO;
    }
    
	private boolean isVersionBlacklisted(String optimusDeploymentServiceUuid, String version) {
		BlacklistedServiceInfo blacklistedServiceInfoSet = blacklistedServiceInfoRepository
				.findByServiceIDAndVersionAndIsValidTrue(optimusDeploymentServiceUuid, version);
		if (blacklistedServiceInfoSet == null) {
			log.info("Version {} for service {} is not blacklisted", version, optimusDeploymentServiceUuid);
			return false;
		} else {
			log.warn("Version {} for service {} is blacklisted", version, optimusDeploymentServiceUuid);
			return true;
		}
	}
    
	public PrStateDTO fetchPrStateDetails(String optimusDeploymentUuid, OptimusDeploymentPR pr)
			throws Exception {
		final PrStateDTO prState = new PrStateDTO();
		String repoName = pr.getRepositoryName();
		prState.setRepositoryName(repoName);
		int prNumber = pr.getPrNumber();
		GHRepository githubRepository = githubService.getRepository(repoName);
		GHPullRequest pullRequest = githubRepository.getPullRequest(prNumber);
		if (pullRequest.getState().equals(GHIssueState.OPEN)) {
			prState.setIsOpen(true);
			log.info("Pull request merging state " + pullRequest.getMergeableState());
			if (pullRequest.getMergeable() != null && pullRequest.getMergeableState()!=null && pullRequest.getMergeableState().equalsIgnoreCase("clean")) {
				prState.setIsMergeable(pullRequest.getMergeable());
			} else {
				log.warn("PR merging state is blocked");
				prState.setIsMergeable(false);
			}
			ExecutorService executionPool = Executors.newFixedThreadPool(2);
			executionPool.submit(() -> {
					try {
						if (githubRepository.getCompare(pullRequest.getBase().getRef(), pullRequest.getHead().getRef())
								.getBehindBy() > 0) {
							log.info("Backmerge is not taken, setting isBehindBaseBranch true ");
							prState.setIsBehindBaseBranch(true);
						}
					} catch (IOException e) {
						log.info("Error encountered while comparing head and base ref for PR " + e.getStackTrace());
					}
			});

			log.info("PR is in Open State");
			executionPool.submit(() -> {
					try {
						List<GHPullRequestReview> prReviewList = pullRequest.listReviews().toList();

						boolean isPrApproved = false;
						if (prReviewList != null && prReviewList.size() > 0) { // TODO for Hriddhi - IDE warning here
							isPrApproved = prReviewList.get(prReviewList.size() - 1).getState()
									.equals(GHPullRequestReviewState.APPROVED); // TODO for Hriddhi - IDE warning here
						}
						prState.setIsApproved(isPrApproved);
						log.info("PR for repo: " + repoName + " PR number: " + prNumber + " is " + isPrApproved);
					} catch (Exception e) {
						log.error("Error while checking if PR is approved " + e.getStackTrace());
					}
				});
			prState.setConflictingTicket(fetchTicketForPRInConflictingDeployment(optimusDeploymentUuid, pr ));
            prState.setConflictingCanary2StableDeploymentTicket(fetchTicketForPRInConflictingCanary2StableDeployment(optimusDeploymentUuid,pr));
			try {
				executionPool.shutdown();
				executionPool.awaitTermination(60, TimeUnit.SECONDS);
			} catch (InterruptedException e) {
				log.error("Error while awaiting termination of execution poll " + e.getStackTrace().toString());
				executionPool.shutdown();
			}
		} else {
			log.warn("PR for repo: " + repoName + " PR number: " + prNumber + " is closed.");
			prState.setIsOpen(false);
		}

		log.info("PR state for repo: " + repoName + " PR number: " + prNumber + " is " + prState);
		return prState;
	}
	
	public String fetchTicketForPRInConflictingCanary2StableDeployment(String optimusDeploymentUuid, OptimusDeploymentPR pr) {

		String currentPRrepoName = pr.getRepositoryName();
		Boolean isRepoCanarized = false;
		String conflictingTicket = null;

		for (OptimusDeploymentServiceEntity odService : pr.getServices()) {
			if (odService.getService().getCanaryStatus() == CanaryStatus.CANARY
					|| odService.getService().getCanaryStatus() == CanaryStatus.STABLE) {
				log.info("Current PR repo {} is canarized ", currentPRrepoName);
				isRepoCanarized = true;				
			}
			
			
		}
		List<OptimusDeployment> conflictingOptimusDeployments = optimusDeploymentRepository
				.findConflictingOptimusDeployment(Arrays.asList(OptimusDeploymentStatus.DRAFT), currentPRrepoName,
						optimusDeploymentUuid, OptimusDeploymentPRStatus.CREATED);

		
		if(isRepoCanarized)
		{
			log.info("Fetching conflicting canary2stable deployment ");
			 conflictingOptimusDeployments = optimusDeploymentRepository
					.findConflictingCanary2StableOptimusDeployment(Arrays.asList(OptimusDeploymentStatus.DRAFT), currentPRrepoName);
			 if(conflictingOptimusDeployments!=null && !conflictingOptimusDeployments.isEmpty()) {
				 
			         conflictingTicket = conflictingOptimusDeployments.get(0).getTicket();
			 
			 }
			 
		}

		log.info("Conflicting canary to stable deployment ticket " + conflictingTicket);

		return conflictingTicket;
	}
	
	public String fetchTicketForPRInConflictingDeployment(String optimusDeploymentUuid, OptimusDeploymentPR pr) {

		String currentPRrepoName = pr.getRepositoryName();
		Boolean isRepoCanarized = false;
		String conflictingTicket = null;
		String currentPrParentBranch = null;
		Boolean isStable = false;

		for (OptimusDeploymentServiceEntity odService : pr.getServices()) {
			if (odService.getService().getCanaryStatus() == CanaryStatus.CANARY
					|| odService.getService().getCanaryStatus() == CanaryStatus.STABLE) {
				log.info("Current PR repo {} is canarized ", currentPRrepoName);
				isRepoCanarized = true;
			}

            currentPrParentBranch = odService.getParentBranch();
			
			if(odService.getService().getCanaryStatus() == CanaryStatus.STABLE)
			{
				isStable = true;
			}
		}
		List<OptimusDeployment> conflictingOptimusDeployments = optimusDeploymentRepository
				.findConflictingOptimusDeployment(Arrays.asList(OptimusDeploymentStatus.DRAFT), currentPRrepoName,
						optimusDeploymentUuid, OptimusDeploymentPRStatus.CREATED);

		if (conflictingOptimusDeployments != null && !conflictingOptimusDeployments.isEmpty()) {
			for (OptimusDeployment conflictingOptimusDeployment : conflictingOptimusDeployments) {
				log.info(
						"Conflicting OptimusDeployment found for current canarised PR Repo {}, checking if paretbranch is same {}",
						currentPRrepoName, currentPrParentBranch);
				for (OptimusDeploymentPR coflictingDeploymentPr : conflictingOptimusDeployment.getPrs()) {
					if (coflictingDeploymentPr.getRepositoryName().equals(currentPRrepoName) && coflictingDeploymentPr
							.getServices().get(0).getParentBranch().equals(currentPrParentBranch)) {

						log.info(
								"Conflicting OptimusDeployment found for current canarised PR Repo {}, current PR Base Branch {} , conflicting PR Base Branch {}",
								currentPRrepoName, currentPrParentBranch,
								coflictingDeploymentPr.getServices().get(0).getParentBranch());
						conflictingTicket = conflictingOptimusDeployment.getTicket();

					}
				}
			}
		}
		
		if(conflictingTicket ==null && isRepoCanarized && isStable)
		{
			 conflictingOptimusDeployments = optimusDeploymentRepository
					.findConflictingCanary2StableOptimusDeployment(Arrays.asList(OptimusDeploymentStatus.DRAFT), currentPRrepoName);
			 if(conflictingOptimusDeployments!=null && !conflictingOptimusDeployments.isEmpty()) {
				 
			         conflictingTicket = conflictingOptimusDeployments.get(0).getTicket();
			 
			 }
			 
		}

		log.info("Conflicting ticket " + conflictingTicket);

		return conflictingTicket;
	}

	private OptimusDeploymentServiceEntity fetchOptimusDeploymentServiceFromOptimusDeployment(
			OptimusDeployment optimusDeployment, String serviceUuid) {
		// TODO Auto-generated method stub
		OptimusDeploymentServiceEntity optimusDeploymentServiceEntity = null;

		for (OptimusDeploymentServiceEntity service : optimusDeployment.getServices()) {
			if (service.getUuid().equals(serviceUuid)) {
				log.info("Match found for optimusDeployment service uuid " + serviceUuid);
				optimusDeploymentServiceEntity = service;
			}
		}

		return optimusDeploymentServiceEntity;
	}

    private ValidationResultDTO initializeValidationResultDTO(String optimusUUID) {
        ValidationResultDTO validationResult = new ValidationResultDTO();
        String validationUuid = CommonUtils.getGuid();
        log.info("Initializing validation result DTO for Validation UUID " + validationUuid);
        validationResult.setOptimusDeploymentUuid(optimusUUID);
        validationResult.setValidationUuid(validationUuid);
        List<String> globalValidationErrors = new ArrayList<>();
        validationResult.setGlobalValidationErrors(globalValidationErrors);
        return validationResult;
    }

  private <T, G> void validate(T state, G validationResult, List<String> rulesList) throws IOException {
    KieSession kieSession = rulesEngineService.getKieSession();
    kieSession.insert(state);
    kieSession.insert(validationResult);
    if(rulesList.size() > 0){
      Collection<KiePackage> packages = kieSession.getKieBase().getKiePackages();
      List<Rule> rulesToRemove = new ArrayList<>();
      for (KiePackage kiePackage : packages) {
        for (Rule rule : kiePackage.getRules()) {
          if (rulesList.contains(rule.getName())) {
            rulesToRemove.add(rule);
          }
        }
      }
      for (Rule rule : rulesToRemove) {
        kieSession.getKieBase().removeRule(rule.getPackageName(), rule.getName());
        log.info("Removed rule "+rule.getName());
      }
    }
    kieSession.fireAllRules();
  }

  public ValidationOverrideResponse fetchRulesToOverride(ValidationResultDTO validationResultDTO,
      String optimusDeploymentUuid, String token)
          throws URISyntaxException, IOException, ParseException {
    OptimusDeployment optimusDeployment = optimusDeploymentRepository.findByUuid(optimusDeploymentUuid);
    Set<OverridableRules> overridableRulesSet = overridableRulesRepository.findByIsOverridableTrue();
    if(overridableRulesSet.isEmpty()){
      log.error("Unable to find overridable rules for validation override");
      throw new BadArgumentsException(ErrorCodes.NO_OVERRIDE_RULES_FOUND);
    }

    Set<OverrideRequestInfo> overrideRequestInfoSet = overrideRequestsRepository.findByOptimusDeploymentUuid(optimusDeploymentUuid);
    List<OverrideRequestInfo> pendingOverrideInfo = Optional.ofNullable(overrideRequestInfoSet).orElse(Collections.emptySet()).stream()
        .filter(i -> i.getValidationOverrideStatus().equals(ValidationOverrideStatus.PENDING))
        .collect(Collectors.toList());

    List<OverrideRequestInfo> nonPendingOverrideInfo = Optional.ofNullable(overrideRequestInfoSet).orElse(Collections.emptySet()).stream()
        .filter(i -> !(i.getValidationOverrideStatus().equals(ValidationOverrideStatus.PENDING)))
        .collect(Collectors.toList());
    ValidationOverrideResponse validationOverrideResponse= new ValidationOverrideResponse();
    if(pendingOverrideInfo.size()>1){
      log.error("more than one validation pending request found for :"+optimusDeploymentUuid);
      throw new BadArgumentsException(ErrorCodes.EXISTING_PENDING_REQUEST);
    }
    else{
      Optional<OverrideRequestInfo> existingOverrideInfo = Optional.ofNullable(getExistingValidationRequest(optimusDeploymentUuid));
      Set<OverrideRequestInfo> existingOverrideInfoSet = new HashSet<>();
      if(existingOverrideInfo.isPresent()){
        existingOverrideInfoSet.add(existingOverrideInfo.get());
        log.error("Pending rule found for :"+optimusDeploymentUuid);
        validationOverrideResponse.setPrOverrideRulesResult(existingOverrideInfo.get().getPrOverrideRulesDTOList());
        validationOverrideResponse.setPendingRequestInfo(existingOverrideInfoSet);
      }
      log.error("Firing new rule fetch request for :"+optimusDeploymentUuid);
      List<PrOverrideRulesDTO> prOverrideRulesDTOList = new ArrayList<>();
      if(validationResultDTO.getPrValidationResult()!=null && !validationResultDTO.getPrValidationResult().isEmpty()) {
        for (PrValidationResultDTO prValidationResultDTO : validationResultDTO.getPrValidationResult()) {

          PrOverrideRulesDTO prOverrideRulesDTO = new PrOverrideRulesDTO();
          prOverrideRulesDTO.setOptimusDeploymentPrUuid(prValidationResultDTO.getOptimusDeploymentPrUuid());

          List<ValidationOverrideInfoDTO> validationOverrideInfoDTOList =  new ArrayList<>();
          for (ValidationInfoDTO validationInfoDTO : prValidationResultDTO.getValidationErrors()) {
            ValidationOverrideInfoDTO validationOverrideInfoDTO = new ValidationOverrideInfoDTO();
            validationOverrideInfoDTO.setValidationInfoDTO(validationInfoDTO);
            // Mongo fetch and set IsOverridable
            validationOverrideInfoDTO.setIsOverridable(isRulesOverridable(overridableRulesSet, validationInfoDTO.getRuleName()));
            validationOverrideInfoDTOList.add(validationOverrideInfoDTO);

            prOverrideRulesDTO.setValidationErrors(validationOverrideInfoDTOList);
          }

          List<ServiceOverrideRulesDTO> serviceOverrideRulesDTOList = new ArrayList<>();
          for (ServiceValidationResultDTO serviceValidationResultDTO : prValidationResultDTO.getServiceValidationResult()) {
              ServiceOverrideRulesDTO serviceOverrideRulesDTO = processServiceValidationOverrideRules(serviceValidationResultDTO,overridableRulesSet);
              serviceOverrideRulesDTOList.add(serviceOverrideRulesDTO);
              prOverrideRulesDTO.setServiceValidationResult(serviceOverrideRulesDTOList);
          }
          prOverrideRulesDTOList.add(prOverrideRulesDTO);
        }
        validationOverrideResponse.setPrOverrideRulesResult(prOverrideRulesDTOList);

    }
      else
      {
          List<ServiceOverrideRulesDTO> serviceWithoutPROverrideRulesDTOList = new ArrayList<>();
          
          for (ServiceValidationResultDTO serviceValidationResultDTO : validationResultDTO.getServiceWithoutPrValidationResult() ) {
              ServiceOverrideRulesDTO serviceWithoutPROverrideRulesDTO = processServiceValidationOverrideRules(serviceValidationResultDTO,overridableRulesSet);
              serviceWithoutPROverrideRulesDTOList.add(serviceWithoutPROverrideRulesDTO);
          }
          validationOverrideResponse.setServiceWithoutPRValidationResult(serviceWithoutPROverrideRulesDTOList);

      }
    }
    List<RequestInfo> requestInfos = new ArrayList<>();
    for (OverrideRequestInfo overrideRequestInfo : nonPendingOverrideInfo) {
      RequestInfo requestInfo = new RequestInfo();
      requestInfo.setRequestUuid(overrideRequestInfo.getRequestUuid());
      requestInfo.setValidationOverrideStatus(overrideRequestInfo.getValidationOverrideStatus());
      requestInfos.add(requestInfo);
    }
    validationOverrideResponse.setRequestInfoList(requestInfos);
    validationOverrideResponse.setBabaInfo(getBabaList(token));
    validationOverrideResponse.setOptimusDeploymentUuid(optimusDeploymentUuid);
    validationOverrideResponse.setTicket(optimusDeployment.getTicket());
    return validationOverrideResponse;
  }

  private ServiceOverrideRulesDTO processServiceValidationOverrideRules(ServiceValidationResultDTO serviceValidationResultDTO, Set<OverridableRules> overridableRulesSet) {
      ServiceOverrideRulesDTO serviceOverrideRulesDTO = new ServiceOverrideRulesDTO();
      serviceOverrideRulesDTO.setOptimusDeploymentServiceUuid(serviceValidationResultDTO.getOptimusDeploymentServiceUuid());

      List<ValidationOverrideInfoDTO> serviceValidationOverrideInfoDTOList =  new ArrayList<>();
      for (ValidationInfoDTO serviceValidationInfoDTO : serviceValidationResultDTO.getValidationErrors()) {
        ValidationOverrideInfoDTO serviceValidationOverrideInfoDTO = new ValidationOverrideInfoDTO();
        serviceValidationOverrideInfoDTO.setValidationInfoDTO(serviceValidationInfoDTO);
        // Mongo fetch and set IsOverridable
        serviceValidationOverrideInfoDTO.setIsOverridable(isRulesOverridable(overridableRulesSet, serviceValidationInfoDTO.getRuleName()));
        serviceValidationOverrideInfoDTOList.add(serviceValidationOverrideInfoDTO);
      }
      serviceOverrideRulesDTO.setValidationErrors(serviceValidationOverrideInfoDTOList);
	return serviceOverrideRulesDTO;
}

public Set<OverrideRequestInfo> revokeRulesRequestForValidation(String optimusDeploymentUuid,
      ValidationOverrideStatus validationOverrideStatus) {
    OverrideRequestInfo existingRequestInfo = getExistingValidationRequest(optimusDeploymentUuid);
    if(!existingRequestInfo.getValidationOverrideStatus().equals(ValidationOverrideStatus.PENDING)){
      log.error(ErrorCodes.UNABLE_TO_REVOKE.getErrorMessage());
      throw new BadArgumentsException(ErrorCodes.UNABLE_TO_REVOKE, "Please refresh to fetch latest state of the request");
    }
    Set<OverrideRequestInfo> revokedOverrideRequestInfoSet = new HashSet<>();
    existingRequestInfo.setValidationOverrideStatus(validationOverrideStatus);
    existingRequestInfo.setUpdatedAt(DateFormatter.getCurrentDateTime());
    revokedOverrideRequestInfoSet.add(existingRequestInfo);
    overrideRequestsRepository.saveAll(revokedOverrideRequestInfoSet);
    return revokedOverrideRequestInfoSet;
  }

  public OverrideRequestInfo getExistingValidationRequest(String optimusDeploymentUuid) {
    OverrideRequestInfo overrideRequestInfo = overrideRequestsRepository.
        findFirstByOptimusDeploymentUuidOrderByCreatedAtDesc(optimusDeploymentUuid);
    return overrideRequestInfo;
  }

  private boolean isRulesOverridable(Set<OverridableRules> overridableRulesSet, String ruleName){
    return overridableRulesSet.stream()
        .anyMatch(rule -> rule.getRuleName().equals(ruleName));
  }

  public boolean isRulesApplicableForValidationOverride(List<PrValidationResultDTO> prValidationResult) {
    Set<OverridableRules> overridableRulesSet = overridableRulesRepository.findByIsOverridableTrue();

    return  Optional.ofNullable(prValidationResult).orElse(Collections.emptyList()).stream()
        .flatMap(prValidationResultDTO ->
            Stream.concat(
                Optional.ofNullable(prValidationResultDTO.getValidationErrors()).orElse(Collections.emptyList()).stream(),
                Optional.ofNullable(prValidationResultDTO.getServiceValidationResult()).orElse(Collections.emptyList())
                    .stream()
                    .flatMap(serviceValidationResultDTO ->
                        Optional.ofNullable(serviceValidationResultDTO.getValidationErrors()).orElse(Collections.emptyList()).stream()
                    )
            )
        )
        .filter(Objects::nonNull)
        .anyMatch(validationInfoDTO -> {
          boolean isValidationOverridable = overridableRulesSet.stream()
              .anyMatch(rule ->
                  rule.getRuleName().equals(Optional.ofNullable(validationInfoDTO.getRuleName()).orElse("")) &&
                      Boolean.TRUE.equals(rule.getIsOverridable())
              );
          return isValidationOverridable;
        });
  }
  
	public boolean isRulesApplicableForValidationOverrideForServiceWithoutPR(
			List<ServiceValidationResultDTO> serviceValidationResults) {
		Set<OverridableRules> overridableRulesSet = overridableRulesRepository.findByIsOverridableTrue();
		return Optional.ofNullable(serviceValidationResults).orElse(Collections.emptyList()).stream()
				.flatMap(serviceValidationResultDTO ->

				Optional.ofNullable(serviceValidationResultDTO.getValidationErrors()).orElse(Collections.emptyList())
						.stream()

				).filter(Objects::nonNull).anyMatch(validationInfoDTO -> {
					boolean isValidationOverridable = overridableRulesSet.stream()
							.anyMatch(rule -> rule.getRuleName()
									.equals(Optional.ofNullable(validationInfoDTO.getRuleName()).orElse(""))
									&& Boolean.TRUE.equals(rule.getIsOverridable()));
					return isValidationOverridable;
				});
	}

  private List<BabaInfo> getBabaList(String token) throws URISyntaxException, IOException, ParseException {
    Set<Baba> babaSet = babaRepository.findByIsValidTrue();
    JWSObject jwsObject = JWSObject.parse(token.trim());
    JWTClaimsSet jwtClaimsSet = JWTClaimsSet.parse(jwsObject.getPayload().toString().trim());
    String audience = jwtClaimsSet.getStringClaim("name");
    log.info("Baba found: "+audience);
    List<BabaInfo> babaInfoList = new ArrayList<>();
    for(Baba babaDetails : babaSet){
      BabaInfo babaInfo = new BabaInfo();
      UserResponseDTO userResponseDTO = paniPuriService.getUserByUuid(babaDetails.getUuid());
      if((userResponseDTO.getUser().getFirstName()+" "+userResponseDTO.getUser().getLastName()).equals(audience)){
        babaInfo.setIsActive(true);
      }
      else{
        babaInfo.setIsActive(false);
      }
      babaInfo.setName(userResponseDTO.getUser().getFirstName()+" "+userResponseDTO.getUser().getLastName());
      babaInfo.setUuid(userResponseDTO.getUser().getUuid());
      babaInfoList.add(babaInfo);
      }

    return babaInfoList;
  }
  public Set<String> fetchJiraTicketsAndValidateJiraForOverrideRequest(ValidationOverrideResponse overrideRulesDTOList,String optimusDeploymentUuid, String token){
      Long serviceId ;
      String serviceName = "" ;
      String deploymentJiraNumber = "";
      String developerName = "";
      Long addedCriticalVulnerability;
      String ddTestId = "";
      String serviceUuid = "";
      log.info("Checking Jira Validity for Override of Optimus Deployment Uuid: {}", optimusDeploymentUuid);
         Set<String> jiraTickets = new HashSet<>();
      for (PrOverrideRulesDTO prOverrideRulesDTO : Optional.ofNullable(overrideRulesDTOList)
              .map(ValidationOverrideResponse::getPrOverrideRulesResult)
              .orElse(Collections.emptyList())) {

          for (ServiceOverrideRulesDTO serviceOverrideRulesDTO : Optional.ofNullable(prOverrideRulesDTO)
                  .map(PrOverrideRulesDTO::getServiceValidationResult)
                  .orElse(Collections.emptyList())) {

              for (ValidationOverrideInfoDTO validationOverrideInfoDTO : Optional.ofNullable(serviceOverrideRulesDTO)
                      .map(ServiceOverrideRulesDTO::getValidationErrors)
                      .orElse(Collections.emptyList())) {

                  if (Optional.ofNullable(validationOverrideInfoDTO)
                          .map(ValidationOverrideInfoDTO::getValidationInfoDTO)
                          .map(ValidationInfoDTO::getRuleName)
                          .filter(ruleName -> ruleName.equals(addedVulnerabilityRuleName))
                          .isPresent()) {
                      if(validationOverrideInfoDTO.getIsSelected()) {
                          String jiraNumber = serviceOverrideRulesDTO.getAddedVulnerabilityJiraNumber();
                          if (StringUtils.isNullOrEmpty(jiraNumber)) {
                              log.warn("Jira Number is null or empty");
                              throw new BadArgumentsException(ErrorCodes.NO_VULNERABILITY_JIRA_TICKET, "Jira Number: " + jiraNumber);
                          } else if (!jiraService.checkIssueLabel(jiraNumber, StringConstants.VULNERABILITY_LABEL.getValue())) {
                              log.warn("Jira Ticket: {} Does not Contains Label {} is not valid", jiraNumber, StringConstants.VULNERABILITY_LABEL.getValue());
                              throw new BadArgumentsException(ErrorCodes.NO_VULNERABILITY_LABEL_IN_JIRA_TICKET, "Jira Number: " + jiraNumber);
                          }
                      }
                  }
              }
          }
      }

      for (PrOverrideRulesDTO prOverrideRulesDTO :
              Optional.ofNullable(overrideRulesDTOList)
                      .map(ValidationOverrideResponse::getPrOverrideRulesResult)
                      .orElse(Collections.emptyList())) {

          for (ServiceOverrideRulesDTO serviceOverrideRulesDTO :
                  Optional.ofNullable(prOverrideRulesDTO)
                          .map(PrOverrideRulesDTO::getServiceValidationResult)
                          .orElse(Collections.emptyList())) {
              try {
                  try{
                      UserResponseDTO userResponseDTO = paniPuriService.getUserInfo(token);
                      developerName = userResponseDTO.getUser().getFirstName() + " " + userResponseDTO.getUser().getLastName();
                  } catch (Exception e){
                      log.error("Error while fetching User Info from the token {} and Error: {}" , token , e.getMessage());
                      throw new RuntimeException(e);
                  }
                  try {
                      OptimusDeploymentServiceEntity optimusDeploymentServiceEntity = optimusDeploymentServiceRepository.findByUuid(serviceOverrideRulesDTO.getOptimusDeploymentServiceUuid());
                       serviceId = optimusDeploymentServiceEntity.getService().getId();
                       serviceName = optimusDeploymentServiceEntity.getService().getName();
                       deploymentJiraNumber = optimusDeploymentServiceEntity.getOptimusDeployment().getTicket();
                       serviceUuid = optimusDeploymentServiceEntity.getService().getUuid();
                  } catch (Exception e) {
                      log.error("Error while fetching Optimus Deployment Service Entity of Uuid:{} and error: {}", serviceOverrideRulesDTO.getOptimusDeploymentServiceUuid(), e.getMessage());
                      throw new RuntimeException(e);
                  }
                  try {
                      addedCriticalVulnerability = vulnerabilityAnalysisService.getVulnerabilityForService(optimusDeploymentUuid, serviceUuid).getAddedCritical();
                      ddTestId = optimusDeploymentSbomRepository.findByOptimusDeploymentServiceUuid(serviceOverrideRulesDTO.getOptimusDeploymentServiceUuid()).getDdTestId();
                  } catch (Exception e) {
                      log.error("Error while fetching Critical Vulnerability Data or Defect DOJO Test ID for Optimus Deployment Uuid:{} and Service ID: {} error: {}", optimusDeploymentUuid, serviceId, e.getMessage());
                      throw new RuntimeException(e);
                  }
                  String deploymentDetailsPageLink = paniPuriConsoleUrl + SpecialCharactersConstants.SLASH + optimusDeploymentUuid;
                  String vulnerabilityDetailsPageLink = defectDojoUri + SpecialCharactersConstants.SLASH + "test" + SpecialCharactersConstants.SLASH + ddTestId;
                  String jiraComment = addedCriticalVulnerability + " critical " + CommonUtils.createHyperLinkText(StringConstants.VULNERABILITIES.getValue(), vulnerabilityDetailsPageLink) + " added into " + serviceName + " as part of Deployment JIRA " + CommonUtils.createHyperLinkText(deploymentJiraNumber, deploymentDetailsPageLink) + " initiated by " + developerName;
                  log.info("Adding Jira Comment: {} in Vulnerability Jira Ticket: {}", jiraComment, serviceOverrideRulesDTO.getAddedVulnerabilityJiraNumber());
                  jiraService.addComment(serviceOverrideRulesDTO.getAddedVulnerabilityJiraNumber(), jiraComment);
                  jiraTickets.add(serviceOverrideRulesDTO.getAddedVulnerabilityJiraNumber());
              } catch (Exception e) {
                  log.error("Error while adding comment into Jira Ticket: {} for Optimus Deployment Uuid: {} with error: {}" , serviceOverrideRulesDTO.getAddedVulnerabilityJiraNumber(), optimusDeploymentUuid, e.getMessage());
              }
          }
      }
      return jiraTickets ;
  }
  public OverrideRequestInfo saveOverrideRequestInMongo(ValidationOverrideResponse overrideRulesDTOList,
      String optimusDeploymentUuid, String babaUuid, String token){

	  
	
    List<PrOverrideRulesDTO> prOverrideRulesDTOList = overrideRulesDTOList.getPrOverrideRulesResult();
    List<ServiceOverrideRulesDTO> serviceWihtoutPROverrideRulesDTO = overrideRulesDTOList.getServiceWithoutPRValidationResult();

    if(prOverrideRulesDTOList!=null && !prOverrideRulesDTOList.isEmpty()) {
	boolean allPrFalseSelected = Optional.ofNullable(prOverrideRulesDTOList )
        .orElse(Collections.emptyList())
        .stream()
        .allMatch(prOverrideRulesDTO ->
            Optional.ofNullable(prOverrideRulesDTO.getValidationErrors())
                .orElse(Collections.emptyList())
                .stream()
                .allMatch(validationError ->
                    !Optional.ofNullable(validationError)
                        .map(ValidationOverrideInfoDTO::getIsSelected)
                        .orElse(false)
                ) &&
                Optional.ofNullable(prOverrideRulesDTO.getServiceValidationResult()).orElse(Collections.emptyList()).stream().allMatch(serviceOverrideRulesDTO ->
                    Optional.ofNullable(serviceOverrideRulesDTO.getValidationErrors())
                        .orElse(Collections.emptyList())
                        .stream()
                        .allMatch(validationError ->
                            !Optional.ofNullable(validationError)
                                .map(ValidationOverrideInfoDTO::getIsSelected)
                                .orElse(false)
                        )
                )
                

        );
	
	 if (allPrFalseSelected) {
	      log.error("No rules selected for override");
	      throw new BadArgumentsException(ErrorCodes.NO_RULES_SELECTED);
	    }
    } else if(serviceWihtoutPROverrideRulesDTO!=null && !serviceWihtoutPROverrideRulesDTO.isEmpty())
	{
    	log.info("Deployment has only services in it");
		boolean allServicesFalseSelected = Optional.ofNullable(serviceWihtoutPROverrideRulesDTO)
				.orElse(Collections.emptyList()).stream()
				.allMatch(serviceOverrideRulesDTO -> Optional.ofNullable(serviceOverrideRulesDTO.getValidationErrors())
						.orElse(Collections.emptyList()).stream()
						.allMatch(validationError -> !Optional.ofNullable(validationError)
								.map(ValidationOverrideInfoDTO::getIsSelected).orElse(false)));

		if (allServicesFalseSelected) {
			log.error("No rules selected for override");
			throw new BadArgumentsException(ErrorCodes.NO_RULES_SELECTED);
		}
	}
    boolean isOverrideAndSelectedNotAllowed = true;
    if(prOverrideRulesDTOList!=null && !prOverrideRulesDTOList.isEmpty()) {
     isOverrideAndSelectedNotAllowed = Optional.ofNullable(prOverrideRulesDTOList)
        .orElse(Collections.emptyList()) // Handle null case by providing an empty list
        .stream()
        .anyMatch(prOverrideRulesDTO ->
            Optional.ofNullable(prOverrideRulesDTO.getValidationErrors())
                .orElse(Collections.emptyList())
                .stream()
                .anyMatch(validationError ->
                    !Optional.ofNullable(validationError)
                        .map(ValidationOverrideInfoDTO::getIsOverridable)
                        .orElse(false) &&
                        Optional.ofNullable(validationError)
                            .map(ValidationOverrideInfoDTO::getIsSelected)
                            .orElse(false)
                ) ||
                Optional.ofNullable(prOverrideRulesDTO.getServiceValidationResult()).orElse(Collections.emptyList()).stream().anyMatch(serviceOverrideRulesDTO ->
                    Optional.ofNullable(serviceOverrideRulesDTO.getValidationErrors())
                        .orElse(Collections.emptyList())
                        .stream()
                        .anyMatch(validationError ->
                            !Optional.ofNullable(validationError)
                                .map(ValidationOverrideInfoDTO::getIsOverridable)
                                .orElse(false) &&
                                Optional.ofNullable(validationError)
                                    .map(ValidationOverrideInfoDTO::getIsSelected)
                                    .orElse(false)
                        )
                )

        );
    }
	else if (serviceWihtoutPROverrideRulesDTO != null && !serviceWihtoutPROverrideRulesDTO.isEmpty()) {

		isOverrideAndSelectedNotAllowed = Optional
				.ofNullable(overrideRulesDTOList.getServiceWithoutPRValidationResult()).orElse(Collections.emptyList())
				.stream()
				.anyMatch(serviceOverrideRulesDTO -> Optional.ofNullable(serviceOverrideRulesDTO.getValidationErrors())
						.orElse(Collections.emptyList()).stream()
						.anyMatch(validationError -> !Optional.ofNullable(validationError)
								.map(ValidationOverrideInfoDTO::getIsOverridable).orElse(false)
								&& Optional.ofNullable(validationError).map(ValidationOverrideInfoDTO::getIsSelected)
										.orElse(false)));
	}

    if (isOverrideAndSelectedNotAllowed) {
      log.error("rule has isOverride as false and isSelected as true");
      throw new BadArgumentsException(ErrorCodes.INVALID_RULES_SELECTED);
    }
    

    Set<OverrideRequestInfo> overrideRequestInfoSet = overrideRequestsRepository.findByOptimusDeploymentUuid(optimusDeploymentUuid);
    List<OverrideRequestInfo> pendingOverrideInfo = overrideRequestInfoSet.stream()
        .filter(i -> i.getValidationOverrideStatus().equals(ValidationOverrideStatus.PENDING))
        .collect(Collectors.toList());

    if(pendingOverrideInfo.size()>=1){
      log.error("Existing validation pending request found for :"+optimusDeploymentUuid);
      throw new BadArgumentsException(ErrorCodes.EXISTING_PENDING_REQUEST);
    }

    String requestUuid = CommonUtils.getGuid();
    UserResponseDTO userResponseDTO = paniPuriService.getUserInfo(token);

    OverrideRequestInfo overrideRequestInfo = new OverrideRequestInfo();
    overrideRequestInfo.setPrOverrideRulesDTOList(prOverrideRulesDTOList);
    overrideRequestInfo.setValidationOverrideStatus(ValidationOverrideStatus.PENDING);
    overrideRequestInfo.setBabaUuid(babaUuid);
    overrideRequestInfo.setUserUuid(userResponseDTO.getUser().getUuid());
    overrideRequestInfo.setRequestUuid(requestUuid);
    overrideRequestInfo.setCreatedAt(DateFormatter.getCurrentDateTime());
    overrideRequestInfo.setUpdatedAt(DateFormatter.getCurrentDateTime());
    overrideRequestInfo.setOptimusDeploymentUuid(optimusDeploymentUuid);
    overrideRequestInfo.setServiceWithoutPRValidationResult(overrideRulesDTOList.getServiceWithoutPRValidationResult());
    overrideRequestInfo.setOverrideReason(overrideRulesDTOList.getOverrideReason());
    overrideRequestsRepository.save(overrideRequestInfo);
    return overrideRequestInfo;
  }

  public OverrideRequestInfo updateOverrideRequestStatus(String requestUuid, ValidationOverrideStatus validationOverrideStatus) {
    OverrideRequestInfo overrideRequestInfo = getOverrideRequestByUuid(requestUuid);
    overrideRequestInfo.setValidationOverrideStatus(validationOverrideStatus);
    overrideRequestInfo.setUpdatedAt(DateFormatter.getCurrentDateTime());
    List<ValidationOverrideInfoDTO> validationErrors;
    if(overrideRequestInfo.getPrOverrideRulesDTOList()!=null && !overrideRequestInfo.getPrOverrideRulesDTOList().isEmpty()) {
    for (PrOverrideRulesDTO prOverrideRulesDTO : overrideRequestInfo.getPrOverrideRulesDTOList()) {
      if(prOverrideRulesDTO.getValidationErrors() !=null){
        validationErrors = prOverrideRulesDTO.getValidationErrors().stream().map(dto ->{
          if (dto.getIsSelected() && validationOverrideStatus.equals(ValidationOverrideStatus.APPROVED)) {
            dto.setIsApproved(true);
          }
          return dto;
        }).collect(Collectors.toList());
        prOverrideRulesDTO.setValidationErrors(validationErrors);
      }
      List<ValidationOverrideInfoDTO> serviceValidationErrors;
      if(prOverrideRulesDTO.getServiceValidationResult() != null){
          for (ServiceOverrideRulesDTO serviceOverrideRulesDTO : prOverrideRulesDTO.getServiceValidationResult()) {
              if(serviceOverrideRulesDTO.getValidationErrors() !=null){
                  serviceValidationErrors = serviceOverrideRulesDTO.getValidationErrors().stream()
                          .map(dto -> {
                              if (dto.getIsSelected() && validationOverrideStatus.equals(ValidationOverrideStatus.APPROVED)) {
                                  dto.setIsApproved(true);
                              }
                              return dto;
                          })
                          .collect(Collectors.toList());
                  serviceOverrideRulesDTO.setValidationErrors(serviceValidationErrors);
              }
          }
      }
      }
    }
    else  if(overrideRequestInfo.getServiceWithoutPRValidationResult()!=null && !overrideRequestInfo.getServiceWithoutPRValidationResult().isEmpty())
    {
        List<ValidationOverrideInfoDTO> serviceValidationErrors;
        for (ServiceOverrideRulesDTO serviceOverrideRulesDTO : overrideRequestInfo.getServiceWithoutPRValidationResult()) {
          if(serviceOverrideRulesDTO.getValidationErrors() !=null){
            serviceValidationErrors = serviceOverrideRulesDTO.getValidationErrors().stream()
                .map(dto -> {
                  if (dto.getIsSelected() && validationOverrideStatus.equals(ValidationOverrideStatus.APPROVED)) {
                    dto.setIsApproved(true);
                  }
                  return dto;
                })
                .collect(Collectors.toList());
            serviceOverrideRulesDTO.setValidationErrors(serviceValidationErrors);
          }
        }
    }
    overrideRequestsRepository.save(overrideRequestInfo);
    return overrideRequestInfo;
  }

  public void acquireLockForValidationApproval(OverrideRequestInfo overrideRequestInfo){
    OptimusDeployment optimusDeployment = optimusDeploymentRepository.findByUuid(overrideRequestInfo.getOptimusDeploymentUuid());
	if(overrideRequestInfo.getPrOverrideRulesDTOList()!=null && !overrideRequestInfo.getPrOverrideRulesDTOList().isEmpty()) {
    for(PrOverrideRulesDTO prOverrideRulesDTO : overrideRequestInfo.getPrOverrideRulesDTOList()){
      List<String> prRules = Optional.ofNullable(prOverrideRulesDTO.getValidationErrors())
          .orElse(Collections.emptyList())
          .stream().filter(validationError -> validationError.getIsSelected())
          .map(validationError -> validationError.getValidationInfoDTO().getRuleName())
          .collect(Collectors.toList());
      String commaPrRules = String.join(", ", prRules);
      if(!prRules.isEmpty()){
        String existingKey = redisService.getKeyForValidationOverride(overrideRequestInfo.getOptimusDeploymentUuid()+
            ":"+prOverrideRulesDTO.getOptimusDeploymentPrUuid());
        if(!existingKey.isEmpty() || existingKey.equalsIgnoreCase("null")){
          commaPrRules = commaPrRules+", "+existingKey;
        }
        log.info("Releasing existing locks for PR validation");
        redisService.releaseLockForValidationOverride(overrideRequestInfo.getOptimusDeploymentUuid()+":"
            +prOverrideRulesDTO.getOptimusDeploymentPrUuid());
        log.info("PR rules list isn't empty, acquiring lock");

        if(optimusDeployment.getType().getName().equals(OptimusDeploymentTypeEnum.HAPROXY)){
            redisService.acquireLockForValidationOverride(overrideRequestInfo.getOptimusDeploymentUuid() + ":"
                    + prOverrideRulesDTO.getOptimusDeploymentPrUuid(), commaPrRules, IntegerConstants.VALIDATION_OVERRIDE_LOCK_EXPIRY_FOR_HAPROXY.getValue());
        } else {
            redisService.acquireLockForValidationOverride(overrideRequestInfo.getOptimusDeploymentUuid() + ":"
                    + prOverrideRulesDTO.getOptimusDeploymentPrUuid(), commaPrRules, IntegerConstants.VALIDATION_OVERRIDE_LOCK_EXPIRY.getValue());
        }
      }

      if(prOverrideRulesDTO.getServiceValidationResult() !=null){
        for (ServiceOverrideRulesDTO serviceOverrideRulesDTO : prOverrideRulesDTO.getServiceValidationResult()) {
          List<String> serviceRules = Optional.ofNullable(serviceOverrideRulesDTO.getValidationErrors())
              .orElse(Collections.emptyList()) // Handle null case by providing an empty list
              .stream().filter(validationError -> validationError.getIsSelected())
              .map(i -> i.getValidationInfoDTO().getRuleName())
              .collect(Collectors.toList());
          String commaServiceRules = String.join(", ", serviceRules);
          if(!serviceRules.isEmpty()){
            String existingKey = redisService.getKeyForValidationOverride(overrideRequestInfo.getOptimusDeploymentUuid()+
                ":"+serviceOverrideRulesDTO.getOptimusDeploymentServiceUuid());
            if(!existingKey.isEmpty() || existingKey.equalsIgnoreCase("null")){
              commaServiceRules = commaServiceRules+", "+existingKey;
            }
            log.info("Releasing existing locks for PR validation");
            redisService.releaseLockForValidationOverride(overrideRequestInfo.getOptimusDeploymentUuid()+":"
                +serviceOverrideRulesDTO.getOptimusDeploymentServiceUuid());
            log.info("Service rules list isn't empty, acquiring lock");

            if(optimusDeployment.getType().getName().equals(OptimusDeploymentTypeEnum.HAPROXY)){
                redisService.acquireLockForValidationOverride(overrideRequestInfo.getOptimusDeploymentUuid()+":"+
                        serviceOverrideRulesDTO.getOptimusDeploymentServiceUuid(), commaServiceRules, IntegerConstants.VALIDATION_OVERRIDE_LOCK_EXPIRY_FOR_HAPROXY.getValue());
            } else {
                redisService.acquireLockForValidationOverride(overrideRequestInfo.getOptimusDeploymentUuid()+":"+
                        serviceOverrideRulesDTO.getOptimusDeploymentServiceUuid(), commaServiceRules, IntegerConstants.VALIDATION_OVERRIDE_LOCK_EXPIRY.getValue());
            }
          }
        }
      }
    }
  }
	else if(overrideRequestInfo.getServiceWithoutPRValidationResult()!=null && !overrideRequestInfo.getServiceWithoutPRValidationResult().isEmpty())
	{
        for (ServiceOverrideRulesDTO serviceOverrideRulesDTO : overrideRequestInfo.getServiceWithoutPRValidationResult()) {
            List<String> serviceRules = Optional.ofNullable(serviceOverrideRulesDTO.getValidationErrors())
                .orElse(Collections.emptyList()) // Handle null case by providing an empty list
                .stream().filter(validationError -> validationError.getIsSelected())
                .map(i -> i.getValidationInfoDTO().getRuleName())
                .collect(Collectors.toList());
            String commaServiceRules = String.join(", ", serviceRules);
            if(!serviceRules.isEmpty()){
              String existingKey = redisService.getKeyForValidationOverride(overrideRequestInfo.getOptimusDeploymentUuid()+
                  ":"+serviceOverrideRulesDTO.getOptimusDeploymentServiceUuid());
              if(!existingKey.isEmpty() || existingKey.equalsIgnoreCase("null")){
                commaServiceRules = commaServiceRules+", "+existingKey;
              }
              log.info("Releasing existing locks for PR validation");
              redisService.releaseLockForValidationOverride(overrideRequestInfo.getOptimusDeploymentUuid()+":"
                  +serviceOverrideRulesDTO.getOptimusDeploymentServiceUuid());
              log.info("Service rules list isn't empty, acquiring lock");
                if(optimusDeployment.getType().getName().equals(OptimusDeploymentTypeEnum.HAPROXY)){
                    redisService.acquireLockForValidationOverride(overrideRequestInfo.getOptimusDeploymentUuid()+":"+
                            serviceOverrideRulesDTO.getOptimusDeploymentServiceUuid(), commaServiceRules, IntegerConstants.VALIDATION_OVERRIDE_LOCK_EXPIRY_FOR_HAPROXY.getValue());
                } else {
                    redisService.acquireLockForValidationOverride(overrideRequestInfo.getOptimusDeploymentUuid()+":"+
                            serviceOverrideRulesDTO.getOptimusDeploymentServiceUuid(), commaServiceRules, IntegerConstants.VALIDATION_OVERRIDE_LOCK_EXPIRY.getValue());
                }
            }
          }
	}
  }

  public List<String> getRulesForValidationOverride(String uuid){
    Optional<String> rulesValue = Optional.ofNullable(redisService.getKeyForValidationOverride(uuid));
    if(rulesValue.isPresent()){
      ArrayList<String> rulesList = new ArrayList<>(Arrays.asList(rulesValue.get().split(", ")));
      return rulesList;
    }else {
      return new ArrayList<>();
    }

  }

  public void verifyHeader(String requestUuid, String token, String userAgent) {
    log.info("agent : "+token.trim());
    if(!(userAgent.trim().contains("Mozilla/5.0 (compatible; Google-Apps-Script;"))){
      log.error(ErrorCodes.NO_SOUP_FOR_USER.getErrorMessage());
      throw new BadArgumentsException(ErrorCodes.NO_SOUP_FOR_USER);
    }
    try {
      JWSObject jwsObject = JWSObject.parse(token.trim());
      JWTClaimsSet jwtClaimsSet = JWTClaimsSet.parse(jwsObject.getPayload().toString().trim());
      String audience = jwtClaimsSet.getAudience().get(0);
      log.info("Baba found: "+audience);
      OverrideRequestInfo overrideRequestInfo = getOverrideRequestByUuid(requestUuid);
      UserResponseDTO babaResponseDTO = paniPuriService.getUserByUuid(overrideRequestInfo.getBabaUuid());
      String requestBaba = babaResponseDTO.getUser().getFirstName()+" "+babaResponseDTO.getUser().getLastName();
      if(!requestBaba.equals(audience)){
        log.error(ErrorCodes.NO_SOUP_FOR_USER.getErrorMessage());
        throw new BadArgumentsException(ErrorCodes.NO_SOUP_FOR_USER);
      }
    } catch (Exception e) {
      log.error(ErrorCodes.NO_SOUP_FOR_USER.getErrorMessage(), e);
      throw new BadArgumentsException(ErrorCodes.NO_SOUP_FOR_USER);
    }
  }

  public void saveThreadInfo(OverrideRequestInfo overrideRequestInfo, String thread) {
    overrideRequestInfo.setThread(thread);
    overrideRequestsRepository.save(overrideRequestInfo);
  }

  public Set<OverrideRequestInfo> updatePendingOverrideRequest(String optimusDeploymentUuid){
    Set<OverrideRequestInfo> expiredOverrideInfoSet = new HashSet<>();
    Optional<OverrideRequestInfo> latestOverrideInfo = Optional.ofNullable(overrideRequestsRepository.
        findFirstByOptimusDeploymentUuidOrderByCreatedAtDesc(optimusDeploymentUuid));
    String currentDateTime = DateFormatter.getCurrentDateInUTC("yyyy-MM-dd HH:mm:ss");
    if(latestOverrideInfo.isPresent()){
      if(latestOverrideInfo.get().getValidationOverrideStatus().equals(ValidationOverrideStatus.PENDING) ||
        latestOverrideInfo.get().getValidationOverrideStatus().equals(ValidationOverrideStatus.APPROVED)){
        long minDifference = DateFormatter.getDateDiffInMinutes(latestOverrideInfo.get().getCreatedAt(), currentDateTime, "yyyy-MM-dd HH:mm:ss");
        log.info("Difference in minutes: " + minDifference);
        if(minDifference > 120){
          latestOverrideInfo = Optional.ofNullable(updateOverrideRequestStatus(
              latestOverrideInfo.get().getRequestUuid(), ValidationOverrideStatus.EXPIRED));
          expiredOverrideInfoSet.add(latestOverrideInfo.get());
        }
      }
    }
    return expiredOverrideInfoSet;
  }

  public OverrideRequestInfo getOverrideRequestByUuid(String requestUuid){
    return overrideRequestsRepository.findByRequestUuid(requestUuid);
  }

     public boolean isDependencyUpdatedInList(DependencyDetails[] foundDependencies, DependencyDetails[] existingDependencies) {
         if (foundDependencies.length != existingDependencies.length) {
             return true;
         }
         return Arrays.stream(foundDependencies)
                 .anyMatch(updatedDependency -> updatedDependency != null && Arrays.stream(existingDependencies)
                         .noneMatch(currentDependency -> currentDependency != null &&
                                 Objects.equals(updatedDependency.key, currentDependency.key) &&
                                 Objects.equals(updatedDependency.value, currentDependency.value)));

     }


     public void saveDependenciesChangesInMongo(OptimusDeploymentDependencyData dependenciesUpdates) {
         optimusDeploymentDependencyDataRepository.save(dependenciesUpdates);
     }

     public Boolean isDependencyUpdatedInCurrentDeployment(ServiceType serviceType, String dependencyIssueFileContent, OptimusDeploymentDependencyData optimusDeploymentDependencyData){
         if(dependencyIssueFileContent != null){
             Map<String, String> newDependencies = new HashMap<>();
             Map<String, String> oldDependencies = new HashMap<>();
             DependencyDetails[] dependencyDetails = optimusDeploymentDependencyData.getDependencyDetails();

             for (DependencyDetails detail : dependencyDetails) {
                 String key = detail.getKey();
                 String value = detail.getValue();
                 oldDependencies.put(key, value);
             }
             try {
                 if (serviceType.equals(ServiceType.MAVEN_JAR) || serviceType.equals(ServiceType.MAVEN_DEPLOYMENT)) {
                     newDependencies = githubService.parsePomDependencies(dependencyIssueFileContent);
                 } else if (serviceType.equals(ServiceType.RELEASE_TAG) || serviceType.equals(ServiceType.UI_CLIENT)) {
                     if(dependencyIssueFileContent.contains("\"ng\": \"ng\"")){
                         newDependencies = githubService.parsePackageJsonDependencies(dependencyIssueFileContent);
                     }
                     else {
                         newDependencies = githubService.parseRequirementsDependencies(dependencyIssueFileContent);
                     }
                 }
             } catch (Exception e) {
                 log.error("Unable to compare dependencies", e);
                 throw new BadArgumentsException(ErrorCodes.UNABLE_TO_PARSE_POM_OR_PACKAGE);
             }
             return compareDependencies(oldDependencies, newDependencies);
         }
         else{
             return false;
         }

     }

     public Boolean compareDependencies(Map<String, String> oldDependencies, Map<String, String> newDependencies){
      Boolean isDependenciesChanged = false;
         // Combine the keys from both maps into a single set to iterate over all unique keys
         Set<String> allKeys = new HashSet<>(newDependencies.keySet());
         allKeys.addAll(oldDependencies.keySet());

         for (String key : allKeys) {
             String newValue = newDependencies.get(key);
             String oldValue = oldDependencies.get(key);

             // Scenario 1: Key exists only in newDependencies (new dependency)
             if(newDependencies.containsKey(key) &&  !oldDependencies.containsKey(key))
             {
            	 isDependenciesChanged=true;
                 break;
             }
             // Scenario 2: Key exists only in oldDependencies (removed dependency)
             else if(!newDependencies.containsKey(key) &&  oldDependencies.containsKey(key))
             {
            	 isDependenciesChanged=true;
                 break;
             } 
             // Scenario 3: Key exists in both maps but the values are different (changed dependency)
             else if( newDependencies.containsKey(key) &&  oldDependencies.containsKey(key) && !Objects.equals(newValue, oldValue))
             {
            	 isDependenciesChanged=true;
                 break;
             }
         }
         log.info("isDependenciesChanged before return: "+ isDependenciesChanged);
      return isDependenciesChanged;
     }

     public DependencyDetails[] getDependenciesInSubsequentReDeployments(ServiceType serviceType, String dependencyIssueFileContent, OptimusDeploymentDependencyData optimusDeploymentDependencyData){
         if(dependencyIssueFileContent != null){
             Map<String, String> newDependencies = new HashMap<>();
             Map<String, String> oldDependencies = new HashMap<>();
             DependencyDetails[] dependencyDetails = optimusDeploymentDependencyData.getDependencyDetails();

             for (DependencyDetails detail : dependencyDetails) {
                 String key = detail.getKey();
                 String value = detail.getValue();
                 oldDependencies.put(key, value);
             }
             try {
                 if (serviceType.equals(ServiceType.MAVEN_JAR) || serviceType.equals(ServiceType.MAVEN_DEPLOYMENT)) {
                     newDependencies = githubService.parsePomDependencies(dependencyIssueFileContent);
                 } else if (serviceType.equals(ServiceType.RELEASE_TAG) || serviceType.equals(ServiceType.UI_CLIENT)) {
                     if(dependencyIssueFileContent.contains("\"ng\": \"ng\"")){
                         newDependencies = githubService.parsePackageJsonDependencies(dependencyIssueFileContent);
                     }
                     else {
                         newDependencies = githubService.parseRequirementsDependencies(dependencyIssueFileContent);
                     }
                 }
             } catch (Exception e) {
                 log.error("Unable to compare dependencies", e);
             }
             boolean isDependenciesChanged = compareDependencies(oldDependencies, newDependencies);
             log.info("isDependenciesChanged in getDependenciesInSubsequentReDeployments: "+ isDependenciesChanged);
             if(isDependenciesChanged){
                 return newDependencies.entrySet().stream()
                         .map(entry -> new DependencyDetails(entry.getKey(), entry.getValue()))
                         .toArray(DependencyDetails[]::new);
             }
             return oldDependencies.entrySet().stream()
                     .map(entry -> new DependencyDetails(entry.getKey(), entry.getValue()))
                     .toArray(DependencyDetails[]::new);
         }
         else{
            return new DependencyDetails[0];
         }

     }


  @Async
  private void runAnalysis(String odServiceUuid, OptimusDeployment optimusDeployment){
      log.info("Starting Analysis of Optimus Deployment "+optimusDeployment.getUuid());
      OptimusDeploymentActionsDTO optimusDeploymentActionsDTO = new OptimusDeploymentActionsDTO();
      List<OptimusDeploymentServiceActionsDTO> optimusDeploymentServiceActionsDTOList = new ArrayList<>();
      OptimusDeploymentServiceActionsDTO optimusDeploymentServiceActionsDTO = new OptimusDeploymentServiceActionsDTO();
      optimusDeploymentServiceActionsDTO.setOptimusDeploymentServiceUuid(odServiceUuid);
      optimusDeploymentServiceActionsDTOList.add(optimusDeploymentServiceActionsDTO);
      optimusDeploymentActionsDTO.setServicesWithoutPr(optimusDeploymentServiceActionsDTOList);
      vulnerabilityAnalysisService.generateSbomForOptimusDeployment(optimusDeployment,optimusDeploymentActionsDTO);
     }

 }
