package com.mykaarma.optimus.controller.advice;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mykaarma.optimus.model.response.AutomationTestResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.mykaarma.optimus.exception.BadArgumentsException;
import com.mykaarma.optimus.exception.OptimusException;
import com.mykaarma.optimus.model.dto.ErrorDTO;
import com.mykaarma.optimus.model.enums.ErrorCodes;
import com.mykaarma.optimus.model.response.ResponseDTO;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpStatusCodeException;

/**
 * <AUTHOR> Tiwari Copyright © 2023 MyKaarma. All rights reserved.
 */
@Slf4j
@ControllerAdvice
@RestController
public class AbstractController {

	@ResponseStatus(HttpStatus.BAD_REQUEST)
	@ExceptionHandler(value = MethodArgumentNotValidException.class)
	public ResponseDTO handleBaseException(HttpServletRequest httpServletRequest,
										   HttpServletResponse httpServletResponse, MethodArgumentNotValidException e) {
		List<ErrorDTO> errors = new ArrayList<>();
		e.getBindingResult().getAllErrors().forEach((error) -> {
			String fieldName = "Error in field: " + ((FieldError) error).getField();
			String errorMessage = error.getDefaultMessage();
			ErrorDTO errorDTO = new ErrorDTO(ErrorCodes.INVALID_REQUEST_DATA, fieldName, errorMessage);
			errors.add(errorDTO);
		});
		log.warn("Validation error occurred : ", e);
		return new ResponseDTO(errors);
	}

	@ResponseStatus(HttpStatus.BAD_REQUEST)
	@ExceptionHandler(value = BadArgumentsException.class)
	public ResponseDTO handleBaseException(HttpServletRequest httpServletRequest,
										   HttpServletResponse httpServletResponse, BadArgumentsException e) {
		return addErrorToResponse(httpServletRequest, httpServletResponse, e.getErrors(), e);
	}

	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ExceptionHandler(value = OptimusException.class)
	public ResponseDTO handleBaseException(HttpServletRequest httpServletRequest,
										   HttpServletResponse httpServletResponse, OptimusException e) {
		return addErrorToResponse(httpServletRequest, httpServletResponse, e.getErrors(), e);
	}

	@ResponseStatus(HttpStatus.BAD_REQUEST)
	@ExceptionHandler(value = ConstraintViolationException.class)
	public ResponseDTO handleBaseException(HttpServletRequest httpServletRequest,
										   HttpServletResponse httpServletResponse, ConstraintViolationException e) {
		return addErrorToResponse(httpServletRequest, httpServletResponse,
				Collections.singletonList(new ErrorDTO(ErrorCodes.INVALID_REQUEST_DATA, e.getMessage())), e);
	}


	@ResponseStatus(HttpStatus.BAD_REQUEST)
	@ExceptionHandler(value = HttpMessageNotReadableException.class)
	public ResponseDTO handleBaseException(HttpServletRequest httpServletRequest,
										   HttpServletResponse httpServletResponse, HttpMessageNotReadableException e) {
		return addErrorToResponse(httpServletRequest, httpServletResponse, Collections.singletonList(new ErrorDTO(ErrorCodes.INVALID_REQUEST_DATA, e.getLocalizedMessage())), e);
	}

	@ResponseStatus(HttpStatus.BAD_REQUEST)
	@ExceptionHandler(value = HttpClientErrorException.BadRequest.class)
	public ResponseDTO handleBadRequestException(HttpServletRequest httpServletRequest,
												 HttpServletResponse httpServletResponse, HttpClientErrorException.BadRequest e) {
		String errorMessage = e.getResponseBodyAsString();
		ObjectMapper objectMapper = new ObjectMapper();
		try {
			JsonNode jsonNode = objectMapper.readTree(errorMessage);
			String errorTitle = jsonNode.get("errors").get(0).get("errorTitle").asText();
			String message = jsonNode.get("errors").get(0).get("errorMessage").asText();
			ErrorDTO errorDTO = new ErrorDTO(ErrorCodes.INVALID_REQUEST_DATA, errorTitle, message);
			List<ErrorDTO> errors = Collections.singletonList(errorDTO);
			return new ResponseDTO(errors);
		} catch (Exception ex) {
			log.error("Error parsing the JSON response body", ex);
		}
		return addErrorToResponse(httpServletRequest, httpServletResponse, Collections.singletonList(new ErrorDTO(ErrorCodes.INVALID_REQUEST_DATA, e.getLocalizedMessage())), e);
	}

	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ExceptionHandler(value = Exception.class)
	public ResponseDTO handleBaseException(HttpServletRequest httpServletRequest,
										   HttpServletResponse httpServletResponse, Exception e) {
		return addErrorToResponse(httpServletRequest, httpServletResponse, Collections.singletonList(new ErrorDTO(ErrorCodes.INVALID_REQUEST_DATA, e.getLocalizedMessage())), e);
	}

	/**
	 * The umbrella exception handler, to log all unhandled exceptions in
	 * application
	 *
	 * @param httpServletRequest
	 * @param httpServletResponse
	 * @param t
	 * @return
	 * @throws Throwable
	 */
	@ExceptionHandler(value = Throwable.class)
	public ResponseDTO handleBaseException(HttpServletRequest httpServletRequest,
										   HttpServletResponse httpServletResponse, Throwable t) throws Throwable {
		if ((t instanceof HttpRequestMethodNotSupportedException) || (t instanceof HttpMessageNotReadableException)) {
			log.warn("Exception occurred in request : " + t);
		} else {
			log.error("Unhandled Exception occurred in request : ", t);
		}
		throw t;
	}

	private ResponseDTO addErrorToResponse(HttpServletRequest httpServletRequest,
										   HttpServletResponse httpServletResponse, List<ErrorDTO> errors, Exception e) {

		log.error("Exception occurred : errors={}", errors, e);

		if(errors == null || errors.isEmpty()) {
			errors  = Collections.singletonList(
					new ErrorDTO(ErrorCodes.INTERNAL_SERVER_EXCEPTION)
			);
		}
		// Make sure no weird exceptions thrown
		for(ErrorDTO error: errors) {
			if(error.getErrorCode() == null) {
				error.setErrorCode(ErrorCodes.INTERNAL_SERVER_EXCEPTION.getErrorCode());
				error.setErrorTitle(ErrorCodes.INTERNAL_SERVER_EXCEPTION.getErrorTitle());
			}
		}

		return new ResponseDTO(errors);
	}

}